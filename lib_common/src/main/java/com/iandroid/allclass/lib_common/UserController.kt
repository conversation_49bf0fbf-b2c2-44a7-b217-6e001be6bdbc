package com.iandroid.allclass.lib_common

import com.iandroid.allclass.lib_common.beans.AuthUserEntity
import com.iandroid.allclass.lib_common.beans.event.UILoggedInEvent
import com.iandroid.allclass.lib_common.beans.event.UILogoutEvent
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.network.HeaderManager
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import com.iandroid.allclass.lib_common.web.WebMsgManger
import com.iandroid.allclass.lib_common.web.config.WebViewConfig
import io.reactivex.disposables.Disposable

/**
 * Created by david on 2020/8/31.
 */
object UserController {
    private var authUserEntity: AuthUserEntity? = null
    var mDisposable: Disposable? = null
    var traceTime: Long = 0L // token error上报时间

    fun getLocalUser(): AuthUserEntity {
        if (authUserEntity == null) {
            var value = SPUtils.getString(AppContext.context, Values.keyAuthUserEntity, "")
            if (!value.isNullOrEmpty()) {
                authUserEntity = value.jsonToObj<AuthUserEntity>()
            }
        }
        if (authUserEntity == null) authUserEntity = AuthUserEntity()
        return authUserEntity!!
    }

    fun login(newUserInfo: AuthUserEntity) {
        this.authUserEntity = newUserInfo
        cacheAuthUserEntity()
        HeaderManager.updateHeaders()
        WebMsgManger.broadcastToWeb(Any().toJsonString(), WebViewConfig.MSG_TYPE_UPDATETOKEN)
        //获取用户信息
        SimpleRxBus.post(UILoggedInEvent(newUserInfo))
    }

    fun loginOut() {
        val logoutUser = getLocalUser()
        SimpleRxBus.post(UILogoutEvent(logoutUser))
        this.authUserEntity = AuthUserEntity()
        cacheAuthUserEntity()
        HeaderManager.updateHeaders()
        AppController.userLogout()
        clearData()
    }

    fun clearData() {
        mDisposable?.dispose()
        mDisposable = null
    }

    fun hasLoggedIn(): Boolean {
        return !getAccessToken().isNullOrEmpty() && !getUserId().isNullOrEmpty()
    }

    fun getUserId(): String {
        return getLocalUser().userId
    }

    fun getImId(): String {
        return getLocalUser().imId
    }

    fun getUserHead(): String {
        return getLocalUser().avatarUrl
    }

    fun getUserName(): String {
        return getLocalUser().nickname
    }

    fun getAccessToken(): String {
        return getLocalUser().auth_token
    }

    fun isMe(userId: String?): Boolean {
        return (userId.orEmpty() == getUserId() || userId.equals(getImId()))
    }

    fun attachAccount(key: String): String {
        return "${key}_{${getUserId()}}"
    }

    private fun cacheAuthUserEntity() {
        SPUtils.put(AppContext.context, Values.keyAuthUserEntity, getLocalUser().toJsonString())
    }

    fun refreshToken() {
        kotlin.runCatching {
            if (hasLoggedIn()) {
                CommonRepository.refreshToken()
            }
        }
    }

    fun AuthUserEntity?.checkValid(): Boolean {
        return this?.run {
            !auth_token.isNullOrEmpty() && !userId.isNullOrEmpty()
        } ?: false
    }

    fun getImToken(): String {
        return getLocalUser().ImToken
    }

    /**
     * 下线操作
     * @param isTokenError 是否token错误导致的
     */
    fun tickOffline(isTokenError: Boolean = false) {
        var topActivity = AppContext.getTopActivity()
        topActivity?.takeIf { !it.isFinishing }?.run {
            this.runOnUiThread {
                if (isTokenError)
                    traceTokenError()
                loginOut()
                routeAction(ActionType.actionToLogin)
            }
        }
    }

    fun resetAccountInfo() {
        getLocalUser().also {
            it.nickname = ""
            it.sexuality = 0
            it.gender = 0
            it.tags = ArrayList()
            it.age = 0
            it.sign = ""
        }
    }

    /**
     * token error跳转登陆页上报
     */
    private fun traceTokenError() {
        // 最快60s上报一次
        if (System.currentTimeMillis() - traceTime < 1000 * 60
            || getUserId().isNullOrEmpty() || getAccessToken().isNullOrEmpty()
        ) return
        traceTime = System.currentTimeMillis()
        CommonRepository.eventTrace(EventKey.K_token_error) {
            "chatterID" to getUserId()
            "token" to getAccessToken()
        }
    }
}