package com.iandroid.allclass.lib_common

/**
created by wangkm
on 2020/9/12.
 */
object AppViewType {
    const val comEmptyView = -2 //空态like
    const val exceptionView = -3 //异常态
    const val userMatchPlaceItemView = -4  //占位
    const val userChatsPlaceItemView = -5   //占位
    const val messageSettingPlaceItemView = -6   //占位
    const val userMessageSettingPlaceItemView = -7   //占位
    const val userPrivacyPlaceItemView = -8  //占位
    const val taskPlaceItemView = -9  //占位
    const val coursePlaceItemView = -10  //占位
    const val questionPlaceItemView = -11  //占位
    const val taskCatalogPlaceItemView = -12  //占位
    const val findChatsPlaceItemView = -13  //占位
    const val taskHistoricalPlaceItemView = -14  //占位
    const val courseSortPlaceItemView = -15  //占位
    const val penaltiesPlaceItemView = -16  //质检处罚骨架占位
    const val balancePlaceItemView = -17  //Balance detail骨架占位
    const val leaveRecordsPlaceItemView = -18  // 班次假期申请记录骨架占位

    const val chatterMatchedItemView = 1  //matched item view
    const val privacyItemView = 2  //私密view

    const val messageSettingItemView = 3  // message setting item view
    const val userMessageSettingItemView = 4  // user message setting item view

    const val taskWeeklyItemView = 5  // task模块Weekly task
    const val taskDetailDataItemView = 6  // task模块图表
    const val taskHistoricalItemView = 7  // task模块历史
    const val courseItemView = 8  // 课程列表
    const val questionItemView = 9  // 课程问卷题目列表
    const val taskCatalogItemView = 10  // 任务模块：任务目录
    const val multipleSendItemView = 11  // 私密内容打包发送的item
    const val findChatsItemView = 12  // findChatsItem
    const val taskHistoricalListItemView = 13  // task模块历史列表
    const val courseSortItemView = 14  // 课程分类列表
    const val penaltiesItemView = 15   //质检处罚列表
    const val balanceItemView = 16   // Balance detail Income\Deduct
    const val withdrawItemView = 17   // Balance detail Withdraw
    const val leaveRecordsItemView = 18   // 班次假期申请记录
    const val taskBannerItemView = 19   // task模块banner活动入口
    const val rankHeadItemView = 20   // 排行榜前三和自己信息 item
    const val rankItemView = 21   // 排行榜 item
    const val featureGuideItemView = 22   // 新手引导汇总页
    const val featureDirectoryItemView = 23   // 新手引导汇总页目录
}