package com.iandroid.allclass.lib_common

import android.content.Context
import android.graphics.Bitmap
import android.widget.ImageView
import androidx.annotation.DrawableRes
import com.bumptech.glide.Glide
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.integration.okhttp3.OkHttpUrlLoader
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.model.GlideUrl
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.module.AppGlideModule
import com.bumptech.glide.request.RequestOptions
import com.iandroid.allclass.lib_basecore.utils.Utils
import com.iandroid.allclass.lib_common.utils.exts.toPx
import jp.wasabeef.glide.transformations.BlurTransformation
import jp.wasabeef.glide.transformations.GrayscaleTransformation
import okhttp3.OkHttpClient
import java.io.InputStream


object GlideLoader {
    const val imgBlurRadius = 100    //图片模糊程度 100
    const val videoBlurRadius = 40  //视频模糊程度 40

    fun ImageView.loadImage(
        context: Context,
        resId: Int
    ) {
        Glide.with(context).load(Utils.getUriFromDrawableRes(context, resId))
    }

    fun ImageView.loadGifImage(
        context: Context,
        resId: Int
    ) {
        Glide.with(context).asGif().load(resId).into(this)
    }

    fun ImageView.loadImage(
        context: Context,
        url: String?,
        roundedCorners: Int = 10.toPx,
        blur: Int = 0,
        isCenterCrop: Boolean = true
    ) {
        //效果叠加处理
        val requestOptions = RequestOptions()
        val transformations = ArrayList<Transformation<Bitmap>>()
        if (isCenterCrop) {
            transformations.add(CenterCrop())
        }
        if (blur > 0) {
            transformations.add(BlurTransformation(blur))
        }
        if (roundedCorners > 0) {
            transformations.add(RoundedCorners(roundedCorners))
        }
        if (transformations.isNotEmpty()) {
            val multiTransformation = MultiTransformation(*transformations.toTypedArray())
            requestOptions.transform(multiTransformation)
        }

        Glide.with(context).load(url)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .placeholder(R.drawable.bg_square_gray_r)
            .apply(requestOptions) //缓存策略,跳过内存缓存【此处应该设置为false，否则列表刷新时会闪一下】
            .error(R.drawable.bg_square_gray_r).into(this)
    }

    fun ImageView.loadVideo(
        context: Context,
        url: String?,
        roundedCorners: Int = 10.toPx,
        blur: Int = 0
    ) {
        Glide.with(context)
            .setDefaultRequestOptions(
                RequestOptions()
                    .frame(0)
                    .centerCrop().apply {
                        if (roundedCorners > 0) {
                            transform(RoundedCorners(roundedCorners))
                        }
                        if (blur > 0) {
                            transform(BlurTransformation(blur))
                        }
                    }
            )
            .load(url)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .into(this)
    }

    fun ImageView.loadImageCircleCrop(
        context: Context,
        url: String?,
        blur: Int = 0,
        grayscale: Boolean = false //灰度效果
    ) {
        var transform = RequestOptions().transform(CircleCrop())

        if (blur > 0) {
            transform = RequestOptions().transform(CircleCrop(), BlurTransformation(blur))
        }

        if (grayscale) {
            transform = RequestOptions().transform(CircleCrop(), GrayscaleTransformation())
        }

        @DrawableRes
        var defaultResourceId: Int = R.drawable.bg_square_gray_s
        Glide.with(context)
            .load(if (url.isNullOrEmpty()) defaultResourceId else url)
            .placeholder(defaultResourceId)
            .error(defaultResourceId)
            .apply(transform)
            .into(this)
    }
}