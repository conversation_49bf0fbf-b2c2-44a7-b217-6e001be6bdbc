package com.iandroid.allclass.lib_common

import com.iandroid.allclass.lib_common.beans.MediaEntity

/**
 * @Desc: 用于全局变量可选变量管理
 * @Created: Quan
 * @Date: 2023/11/14
 */
object SelectedHelper {

    private var photoSelected = ArrayList<MediaEntity?>()
    private var videoSelected = ArrayList<MediaEntity?>()

    /**
     * 返回已选择的图片
     */
    fun getPhotoSelected(): ArrayList<MediaEntity?> {
        return photoSelected
    }

    /**
     * 返回已选择的视频
     */
    fun getVideoSelected(): ArrayList<MediaEntity?> {
        return videoSelected
    }

    fun getSelected(isShowPhoto: Boolean): ArrayList<MediaEntity?> {
        return if (isShowPhoto) {
            getPhotoSelected()
        } else {
            getVideoSelected()
        }
    }

    fun removeSelected(id: Long) {
        for ((index, mediaEntity) in getPhotoSelected().withIndex()) {
            if (mediaEntity?.id == id) {
                getPhotoSelected().removeAt(index)
                return
            }
        }
        for ((index, mediaEntity) in getVideoSelected().withIndex()) {
            if (mediaEntity?.id == id) {
                getVideoSelected().removeAt(index)
                return
            }
        }
    }

    fun clearSelected() {
        photoSelected.clear()
        videoSelected.clear()
    }

}