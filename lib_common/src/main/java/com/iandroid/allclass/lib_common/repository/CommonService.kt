package com.iandroid.allclass.lib_common.repository

import com.iandroid.allclass.lib_common.beans.AuthUserEntity
import com.iandroid.allclass.lib_common.beans.GlobalConfigEntity
import com.iandroid.allclass.lib_common.beans.UploadData
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.beans.UserOnlineEntity
import com.iandroid.allclass.lib_common.beans.base.HttpResult
import com.iandroid.allclass.lib_common.network.DomainProvider
import io.reactivex.Completable
import io.reactivex.Single
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Url

/**
 * Created by david on 2020/11/4.
 */
interface CommonService {
    //获取app配置信息
    @Headers(DomainProvider.DOMAIN_API)
    @POST("global/config")
    fun getAppDataConfig(): Single<HttpResult<GlobalConfigEntity>>

    @Headers(DomainProvider.DOMAIN_API)
    @POST("tool/reg_uploadurl")
    fun requestUploadFile(@Body params: RequestBody): Single<HttpResult<UploadData>>

    @Headers(DomainProvider.DOMAIN_NONE)
    @PUT
    fun uploadFile(
        @Url uploadUrl: String,
        @Body file: RequestBody
    ): Completable

    //刷新token
    @Headers(DomainProvider.DOMAIN_API)
    @POST("tool/tokenrefresh")
    fun refreshToken(): Single<HttpResult<AuthUserEntity>>

    //推送Token
    @Headers(DomainProvider.DOMAIN_API)
    @POST("tool/updatepush")
    @FormUrlEncoded
    fun changePushToken(
        @Field("push_token") pushToken: String
    ): Single<HttpResult<Any>>

    //推送Token
    @Headers(DomainProvider.DOMAIN_API)
    @POST("tool/updatepush")
    @FormUrlEncoded
    fun deletePushToken(
        @Header("UserId") userId: String,
        @Header("Authorization") token: String,
        @Field("push_token") pushToken: String
    ): Single<HttpResult<Any>>

    @Headers(DomainProvider.DOMAIN_API)
    @POST("tool/report")
    @FormUrlEncoded
    fun reportUser(
        @Field("user_id") toUserId: String,
        @Field("from") from: Int,
        @Field("report_id") reportId: Int
    ): Single<HttpResult<Any?>>

    @Headers(DomainProvider.DOMAIN_API)
    @POST("tool/otherprofile")
    @FormUrlEncoded
    fun getUserInfo(
        @Field("user_id") userId: String
    ): Single<HttpResult<UserEntity>>

    @Headers(DomainProvider.DOMAIN_API)
    @POST("tool/chat/recall")
    @FormUrlEncoded
    fun chatRecall(
        @Field("msg_id") msgId: String,
        @Field("sender_id") senderId: String,
        @Field("target_id") targetId: String,
        @Field("sent_time") sentTime: Long,
    ): Single<HttpResult<Any>>

    @Headers(DomainProvider.DOMAIN_API)
    @POST("global/pushtrace")
    @FormUrlEncoded
    fun pushTrace(
        @Field("trace_id") msgId: Int
    ): Single<HttpResult<Any>>

    //查询目前在线的好友id列表
    @Headers(DomainProvider.DOMAIN_API)
    @POST("tool/v2/online_ulist")
    fun queryOnlineUser(): Single<HttpResult<ArrayList<UserOnlineEntity>>>

    @Headers(DomainProvider.DOMAIN_API)
    @POST("tool/event/trace")
    @FormUrlEncoded
    fun eventTrace(
        @Field("key") key: String,
        @Field("param") param: String
    ): Single<HttpResult<Any>>

    @Headers(DomainProvider.DOMAIN_API)
    @POST("tool/page_popup/update")
    @FormUrlEncoded
    fun guideRead(@Field("pop_up") tag: String): Single<HttpResult<Any>>
}