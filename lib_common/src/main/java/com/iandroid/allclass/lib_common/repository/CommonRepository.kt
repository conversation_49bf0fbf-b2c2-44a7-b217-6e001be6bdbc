package com.iandroid.allclass.lib_common.repository

import android.util.Log
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.UserController.checkValid
import com.iandroid.allclass.lib_common.beans.UpLoadImgBean
import com.iandroid.allclass.lib_common.beans.UploadResult
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.beans.UserOnlineEntity
import com.iandroid.allclass.lib_common.beans.base.checkData
import com.iandroid.allclass.lib_common.beans.base.checkDataWithRetmsg
import com.iandroid.allclass.lib_common.beans.event.UIOpenAdEvent
import com.iandroid.allclass.lib_common.event.ConfigGettedEvent
import com.iandroid.allclass.lib_common.event.EventParamsBuilder
import com.iandroid.allclass.lib_common.event.UIEventReportUserCallback
import com.iandroid.allclass.lib_common.network.ErrorCodeCheckUtils
import com.iandroid.allclass.lib_common.network.ServiceFactory
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import okhttp3.FormBody
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import java.io.File

/**
 * Created by david on 2020/11/4.
 */
object CommonRepository {

    private val commonApi = ServiceFactory.get(CommonService::class.java)

    /**
     * 刷新token
     */
    fun refreshToken() {
        commonApi.refreshToken()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ httpresult ->
                if (httpresult.isSuccessful()) {
                    httpresult.data?.takeIf { it.checkValid() }?.run {
                        UserController.login(this)
                    }
                } else {
                    UserController.loginOut()
                    ToastUtils.showToast(httpresult.msg)
                }
            }, {
            })
    }

    /**
     * 获取app配置信息
     */
    fun getAppDataConfig(jumpBlock: ((Boolean) -> Unit)? = null): Disposable {
        return commonApi.getAppDataConfig()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ config ->
                config?.data?.apply {
                    AppController.updateGlobalDataConfig(this)
                    this.appOpening?.takeIf { it.banner != null }?.also {
                        SimpleRxBus.post(UIOpenAdEvent(it))
                    }
                }
                jumpBlock?.invoke(true)
                SimpleRxBus.post(ConfigGettedEvent())
            }, {
                jumpBlock?.invoke(false)
                SimpleRxBus.post(ConfigGettedEvent())
            })
    }

    fun uploadFile(
        file: File,
        fileMd5: String,
        uploadBean: UpLoadImgBean?,
        sequence: Int = 0
    ): Single<UploadResult> {
        val builder = FormBody.Builder()
        builder.add("file_name", fileMd5)
        builder.add("content_type", "")
        val build = builder.build()

        return commonApi.requestUploadFile(build)
            .subscribeOn(Schedulers.io())
            .map {
                it.checkData()
            }
            .flatMap { uploadData ->
                val uploadUrl: String = uploadData.uploadUrl
                val requestBody = RequestBody.create("".toMediaTypeOrNull(), file)
                commonApi.uploadFile(uploadUrl, requestBody)
                    .toSingle {
                        UploadResult(uploadData.cdnUrl, sequence)
                    }
            }
            .doOnError {
                Throwable("upload to file failed! ")
            }
            .observeOn(AndroidSchedulers.mainThread())
    }


    /**
     * 更新推送token fcm|getui
     */
    fun changePushToken(
        token: String
    ): Single<Any> {
        return commonApi.changePushToken(token)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .map { it.checkData() }
    }

    /**
     * 删除推送token fcm|getui
     */
    fun deletePushToken(
        userId: String,
        accessToken: String
    ): Single<Any> {
        return commonApi.deletePushToken(userId, accessToken, "")
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .map { it.checkData() }
    }

    /**
     * 新手引导设置已读
     */
    fun guideRead(tag: String) {
        commonApi.guideRead(tag)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .map { it.checkDataWithRetmsg() }
            .subscribe({ }, {})
    }

    /**
     * 举报用户
     */
    fun reportUser(toUserId: String, from: Int, reportId: Int) {
        commonApi.reportUser(toUserId, from, reportId)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .map { it.checkDataWithRetmsg() }
            .subscribe({
                ToastUtils.showToast(it)
                SimpleRxBus.post(UIEventReportUserCallback(toUserId))
            }, {
                ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
            })
    }

    fun getUserInfo(userId: String, block: (userInfo: UserEntity) -> Unit) {
        commonApi.getUserInfo(userId)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({
                it.data?.apply {
                    block.invoke(this)
                }
            }, {

            })
    }

    /**
     * App消息撤回
     */
    fun chatRecall(msgId: String, senderId: String, targetId: String, sentTime: Long, callback: (Boolean) -> Unit) {
        commonApi.chatRecall(msgId, senderId, targetId, sentTime)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .map { it.checkData() }
            .subscribe({
                callback.invoke(true)
            }, {
                callback.invoke(false)
                ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
            })
    }

    /**
     * 离线推送的点击回传
     */
    fun pushTrace(trace_id: Int) {
        commonApi.pushTrace(trace_id)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({
                Log.d("routeAction", "pushTrace t")
            }, {
                Log.d("routeAction", "pushTrace f")
            })
    }

    /**
     * 查询在线用户列表
     */
    fun queryOnlineUser(block: (Boolean, ArrayList<UserOnlineEntity>?) -> Unit) {
        commonApi.queryOnlineUser()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({
                block.invoke(true, it.checkData())
            }, {
                block.invoke(false, null)
            })
    }


    /**
     * 数据埋点
     */
    fun eventTrace(key: String, block: (EventParamsBuilder.() -> Unit)? = null) {
        val builder = EventParamsBuilder()
        block?.invoke(builder)
        val params = builder.build()
        commonApi.eventTrace(key, if (params.isEmpty()) "" else params.toJsonString())
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).subscribe({}, {})
    }

}