package com.iandroid.allclass.lib_common

import com.iandroid.allclass.lib_common.beans.ApiDataResponse
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types

// a generic serialization adapter
object MoshiParser {
    val moshi = Moshi.Builder().build()

    @JvmStatic
    fun <T> parseObjectOrNull(json: String?, type: Class<T>): T? {
        return try {
            moshi.adapter(type).fromJson(json!!)
        } catch (e: Exception) {
            null
        }
    }

    @JvmStatic
    fun <T> objectToJson(obj: T?, type: Class<T>): String {
        return try {
            moshi.adapter(type).toJson(obj)
        } catch (e: Exception) {
            "{}"
        }
    }

    @JvmStatic
    fun <T> parseListOrNull(json: String?, type: Class<T>): List<T>? {
        return try {
            Types.newParameterizedType(List::class.java, type).let {
                moshi.adapter<List<T>>(it)
            }.fromJson(json!!)
        } catch (e: Exception) {
            null
        }
    }

    @JvmStatic
    fun <T> parseApiDataOrNull(json: String?, dataClass: Class<T>): ApiDataResponse<T>? {
        return try {
            val type = Types.newParameterizedType(ApiDataResponse::class.java, dataClass)
            val adapter: com.squareup.moshi.JsonAdapter<ApiDataResponse<T>> = moshi.adapter(type)
            adapter.fromJson(json!!)
        } catch (e: Exception) {
            null
        }
    }
}

// extension
inline fun <reified T> Moshi.listAdapter(): JsonAdapter<List<T>> =
    Types.newParameterizedType(
        List::class.java,
        T::class.java
    ).let { this.adapter<List<T>>(it) }
