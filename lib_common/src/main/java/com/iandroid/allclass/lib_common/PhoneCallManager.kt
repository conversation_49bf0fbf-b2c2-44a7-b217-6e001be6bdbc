package com.iandroid.allclass.lib_common

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.telephony.PhoneStateListener
import android.telephony.TelephonyManager

import android.telephony.ServiceState
import android.util.Log
import com.iandroid.allclass.lib_common.beans.event.UIPhoneCallStateEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus


object PhoneCallManager {
    fun phoneIsInUse(context: Context?): Boolean {
        val telephonyManager =
            context?.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
        return telephonyManager?.let { it.callState != TelephonyManager.CALL_STATE_IDLE } ?: false
    }
}

enum class PhoneCallState {
    IDLE, OUTGOING, RINGING, OFF_HOOK
}


class PhoneReceiver : BroadcastReceiver() {

    private val TAG = "PhoneReceiver"

    private val customPhoneStateListener = object : PhoneStateListener() {
        override fun onServiceStateChanged(serviceState: ServiceState) {
            super.onServiceStateChanged(serviceState)
        }

        override fun onCallStateChanged(state: Int, incomingNumber: String) {
            when (state) {
                TelephonyManager.CALL_STATE_IDLE -> { // 电话挂断
                    SimpleRxBus.post(UIPhoneCallStateEvent(PhoneCallState.IDLE))
                    Log.d(TAG, "CALL_STATE_IDLE")
                }
                TelephonyManager.CALL_STATE_RINGING -> { // 电话响铃
                    SimpleRxBus.post(UIPhoneCallStateEvent(PhoneCallState.RINGING))
                    Log.d(TAG, "CALL_STATE_RINGING")
                }
                TelephonyManager.CALL_STATE_OFFHOOK -> { // 来电接通 或者 去电，去电接通  但是没法区分
                    SimpleRxBus.post(UIPhoneCallStateEvent(PhoneCallState.OFF_HOOK))
                    Log.d(TAG, "CALL_STATE_OFFHOOK")
                }
            }
        }
    }


    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_NEW_OUTGOING_CALL) {
            SimpleRxBus.post(UIPhoneCallStateEvent(PhoneCallState.OUTGOING))
            Log.d(TAG, "CALL_STATE_OUTGOING")
        } else {
            val tm = context?.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
            tm?.listen(customPhoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
        }
    }

}