package com.iandroid.allclass.lib_common.beans.base

import com.iandroid.allclass.lib_common.route.bean.ActionEntity


/**
 * Created by david on 2020/8/28.
 */
class HttpResult<T> {
    var code = 0//返回码

    var msg: String? = null //返回信息，有错误时 是错误描述i，成功时建议为空

    var data: T? = null //消息内容，

    var action: ActionEntity? = null    //路由

    fun isSuccessful(): Boolean {
        return 0 == code
    }
}

inline fun <reified T> HttpResult<T>.checkDataWithRetmsg(): String {
    if (this.isSuccessful()) {
        return msg.orEmpty()
    } else {
        throw ApiException(code, msg.orEmpty())
    }
}

inline fun <reified T> HttpResult<T>.checkData(default: T? = null): T? {
    if (this.isSuccessful()) {
        return data?.takeIf { it is T }?.run {
            data as T
        } ?: kotlin.runCatching {
            val clz = T::class.java
            clz.getDeclaredConstructor()?.run {
                isAccessible = true
                newInstance()
            }
        }.getOrNull() ?: default
    } else {
        throw ApiException(code, msg.orEmpty())
    }
}

inline fun <reified T> HttpResult<T>.checkDataWithHttpHead(): HttpResult<T>? {
    if (this.isSuccessful()) {
        return this
    } else {
        throw ApiException(code, msg.orEmpty())
    }
}


