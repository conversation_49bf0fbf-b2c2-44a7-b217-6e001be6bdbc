package com.iandroid.allclass.lib_common.web.js

import android.content.Context
import android.os.Build
import android.view.View
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebView
import com.iandroid.allclass.lib_basecore.view.CommonPageStatusView
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.R
import com.iandroid.allclass.lib_common.beans.AlbumActionEntity
import com.iandroid.allclass.lib_common.beans.ImmersiveEntity
import com.iandroid.allclass.lib_common.beans.RightTitleEntity
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.web.`interface`.IWebpendantPresenter
import com.iandroid.allclass.lib_common.web.view.LangWebView

/**
created by wangkm
on 2020/9/22.
 */
open class JsSpecialUiImpl(
    var statusView: CommonPageStatusView?,
    var webView: WebView,
    var dialogRootView: View? = null,
    var iWebpendantPresenter: IWebpendantPresenter? = null
) :
    JsSpecialUi {

    override fun startLoadUrl(url: String) {
        webView?.show(statusView == null)
        statusView?.showLoading(null)
        statusView?.show(true)
    }

    override fun DoJSCloseWindow(viewID: String?) {
    }

    override fun onReceivedTitle(title: String?) {
    }

    override fun DoJSStatusBar(immersiveEntity: ImmersiveEntity) {
    }

    override fun DoJSUpdateTitlebarRightAction(rightTitleEntity: RightTitleEntity?) {
    }

    override fun DoJSPhotoAlbum(albumActionEntity: AlbumActionEntity) {
    }

    override fun onProgressChanged(view: WebView, newProgress: Int) {
        if (newProgress >= 90) {
            statusView?.hideLoading()
            statusView?.show(false)
            webView?.show(true)
        }
    }

    override fun onPageFinished(view: WebView, url: String) {
        statusView?.hideLoading()
        statusView?.show(false)
        webView?.show(true)
    }

    override fun onReceivedError(
        context: Context,
        url: String?,
        request: WebResourceRequest?,
        error: WebResourceError?,
        errorMsg: String?
    ) {
        statusView?.hideLoading()
        statusView?.showException(0, null)
        dialogRootView?.setOnClickListener {
            DoJSCloseWindow(null)
        }
        statusView?.tvReloadBtn?.setOnClickListener {
            webView?.reload()
        }
    }

    override fun regMsgType(viewID: String?, isReg: Boolean, data: String) {
        iWebpendantPresenter?.regMsgType(viewID, isReg, data)
    }

    override fun getIWebpendantPresenterObject(): IWebpendantPresenter? {
        return iWebpendantPresenter
    }

    override fun setIWebpendantPresenterObject(iWebpendantPresenterObject: IWebpendantPresenter) {
        this.iWebpendantPresenter = iWebpendantPresenterObject
    }

    override fun JSBodySizeChange(viewId: String?, width: Int, height: Int) {
    }

    override fun hideThisWeb(viewId: String?) {
    }

    override fun JSGetParentWindowType(viewID: String?): Int {
        return 0
    }

    override fun JsUserSpaceBack() {
    }

    override fun JsGetReportData(): String {
        return ""
    }

    override fun JSPageFinished(viewID: String?) {
    }

    override fun JSPageInited(viewID: String?) {
    }

    override fun JSGetWebPageParam(): String? {
        return ""
    }

    override fun JSGetPreloadPageParam(viewID: String?): String {
        return ""
    }

}