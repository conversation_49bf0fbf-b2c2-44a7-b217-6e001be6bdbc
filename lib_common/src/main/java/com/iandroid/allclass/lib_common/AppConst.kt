package com.iandroid.allclass.lib_common

/**
created by wangkm
on 2020/9/15.
 */

class Values {
    companion object {
        const val ENV_DEV = "dev"
        const val ENV_TEST = "test"
        const val ENV_PRE = "pre"
        const val ENV_PROD = "prod"

        val flutterWaitcode = 10001

        //当前运行环境
        var env_product = 0 //线上
        var env_pre = 3 //预发布
        var env_test = 2 //测试
        var env_dev = 1 //开发
        var api_env = env_product //0、线上， 3，预发布，其他测试环境

        //waitfor result code
        const val waitcode_choosepic_from_album = 30001   //从系统相册选图片
        const val waitcode_choosepic_from_camera = 30002   //从相机拍照选图片
        const val waitcode_crop_image = 30003                //裁剪图片

        //other
        const val code_success = 0
        const val code_nonet = -11111
        const val path_clip_image = "clip_image"
        const val crop_photo_size = 600
        const val crop_cover_size = 800
        const val min_image_size = 400
        const val welComeMaxTime = 2L
        const val fristPage = 1   //首页页码
        const val intentJsonParam = "intentJsonParam"
        const val userJsonParam = "userJsonParam"
        const val intentParcelableParam = "parcelableParam"
        const val intentStringParam = "stringParam"
        const val keyAuthUserEntity = "keyAuthUserEntity"
        const val keyGlobalEnvType = "keyEnvType"
        const val keyGlobalDataConfig = "keyglobaldataconfig"
        const val keyGlobalResCache = "keyglobalrescache"
        const val keyTaskID = "keyTaskID"
        const val keyOnlineQueryTime = "keyOnlineQueryTime" // 最近一次查询在线用户列表的时间
        const val keyOnlineQueryIds = "keyOnlineQueryIds" // 最近一次查询在线用户列表的id
        const val keyCheckMPCNotice = "keyCheckMPCNotice" // 最近一次更新mpc的日期
        const val keyMPCNoticeRead = "keyMPCNoticeRead" // 更新的mpc是否已读
        const val keyGoalsNotice2 = "keyGoalsNotice2" // 试岗进度弹窗显示，100L代表弹过完成弹窗，否则记录时间戳
        const val keyRepossessionNotice = "keyRepossessionNotice" // 主播账号回收预警弹窗显示记录时间戳
        const val keyCanAddModelNotice = "keyCanAddModelNotice" // 主播账号可以新增model的弹窗提审
        const val keyCheckCancelLeave = "keyCheckCancelLeave" // 显示取消今日请假弹窗
        const val keyCheckAppList = "keyCheckAppList" // 最近一次上报app列表的时间

        // message setting type
        const val messageSettingTypeGreeting = 0
        const val messageSettingTypeMessage = 1

        /// 缓存H5钱包数据
        const val ALBUM_IMAGE_CHOOSE_WAIT_CODE = 10011

        //性别
        const val sexunknown = 0
        const val male = 1
        const val female = 2
        const val all = 3

        // 服务条款 隐私条款链接
        const val KEY_TERMS = "terms"
        const val KEY_PRIVACY = "privacy"

        const val RES_DIR = "resource"
        const val APK_DIR = "newapk"

        const val api_err_code_logout = 1002

        //  昵称最多多少汉字
        const val nicLimitNum = 8

        const val share_copy = "link"
        const val share_ins = "ins"
        const val share_tt = "tt"
        const val share_fb = "fb"

        // task
        const val taskTypeDay = 0 // 日任务类型
        const val taskTypeWeek = 1 // 周任务类型

        // app定义的使用默认地点时区
        const val chatDefaultTimeZone = "America/New_York"

    }
}