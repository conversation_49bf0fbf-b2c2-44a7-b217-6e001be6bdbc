package com.iandroid.allclass.lib_common.lifecycle

import androidx.annotation.MainThread
import androidx.annotation.NonNull
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer

/**
 *
 * Created by david on 2020/9/2.
 */
class MutableLiveEvent<T> {

    private val liveData = InternalLiveData<T>()
    private var nullValue: T? = null

    @MainThread
    fun observe(@NonNull owner: LifecycleOwner, @NonNull observer: Observer<in T>) {
        liveData.addObserve(owner, observer)
    }

    @MainThread
    fun postValue(value: T?) {
        nullValue = value
        nullValue?.let { liveData.postValue(it) }
    }

    @MainThread
    fun setValue(value: T?) {
        nullValue = value
        nullValue?.let { liveData.value = it }
    }

    @MainThread
    fun getValue(): T? = nullValue?.let { liveData.value } ?: null


    class InternalLiveData<T> : MutableLiveData<T>() {
        private val map = mutableMapOf<Observer<in T>, Bo<PERSON>an>()

        fun addObserve(@NonNull owner: LifecycleOwner, @NonNull observer: Observer<in T>) {

            map[observer] = false
            observe(owner, Observer {
                if (map[observer] == true)
                    observer.onChanged(it)
            })

        }

        override fun setValue(value: T?) {
            //active observe callback after value changed.
            map.forEach { map[it.key] = true }
            super.setValue(value)
        }


        override fun removeObserver(observer: Observer<in T>) {
            super.removeObserver(observer)
            val delete = mutableListOf<Observer<in T>>()
            map.forEach {
                if (it.key == observer) delete.add(observer)
            }

            delete.forEach { map.remove(it)}
        }


    }
}