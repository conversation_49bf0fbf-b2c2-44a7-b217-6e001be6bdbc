package com.iandroid.allclass.lib_common

import android.app.Application
import android.content.IntentFilter
import android.util.Log
import com.iandroid.allclass.lib_common.beans.AppReportItemEntity
import com.iandroid.allclass.lib_common.beans.AppUpdateEntity
import com.iandroid.allclass.lib_common.beans.ChatLvl
import com.iandroid.allclass.lib_common.beans.GlobalConfigEntity
import com.iandroid.allclass.lib_common.beans.UnlockLvl
import com.iandroid.allclass.lib_common.core.IIMCoreCallBack
import com.iandroid.allclass.lib_common.core.RvResolver
import com.iandroid.allclass.lib_common.download.ResourceManager
import com.iandroid.allclass.lib_common.network.DomainProvider
import com.iandroid.allclass.lib_common.network.HeaderManager
import com.iandroid.allclass.lib_common.network.state.NetworkStateReceiver
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import io.reactivex.plugins.RxJavaPlugins


/**
created by wangkm
on 2020/9/2.
 */
object AppController {
    var appChannel: String = ""
    private var globalConfig: GlobalConfigEntity = GlobalConfigEntity()
    var appUpdateEntity: AppUpdateEntity? = null
    var applicationBootFlag = false
    var iIMCoreCallBack: IIMCoreCallBack? = null
    private const val FOLDER_FRESCO_CACHE = "fresco_cache"
    var systemConfigOk = false
    var appInBackground = false // true:app切到了后台
    var inBackTime = 0L // app切到了后台上报状态接口的时间戳

    /**
     * app启动初始化
     */
    fun appStartup(
        application: Application,
        versionCode: Int,
        versionName: String,
        clientName: String,
        apiEnv: String,
        appChannel: String,
        iIMCoreCallBack: IIMCoreCallBack,
    ) {
        this.iIMCoreCallBack = iIMCoreCallBack
        AppContext.context = application.applicationContext
        AppContext.versionCode = versionCode
        AppContext.versionName = versionName
        AppContext.clientName = clientName

        apiEnv?.takeIf { it.isNotEmpty() }?.run {
            Values.api_env = (if (this == "test") Values.env_test
            else {
                if (this == "dev") Values.env_dev else Values.env_product
            })
            if (Values.api_env == Values.env_product)
                DomainProvider.switchEnv(Values.api_env)
        }
        this.appChannel = appChannel

        application.registerActivityLifecycleCallbacks(AppContext)
        // 获取上次记录的公共配置
        getGlobalDataConfig()
        // 获取云端公共配置
        CommonRepository.getAppDataConfig {
            systemConfigOk = true
        }
        //刷新token
        UserController.refreshToken()
        //RecyclerView
        RvResolver.init(AppContext.context)
        //同步语言
        val intentFilter = IntentFilter()
        intentFilter.addAction("android.net.conn.CONNECTIVITY_CHANGE")
        application.registerReceiver(NetworkStateReceiver(), intentFilter)

        RxJavaPlugins.setErrorHandler { e: Throwable? ->
            Log.e("RxErrorHandler", "cause: ${e?.cause} \n message: ${e?.message}")
        }
        HeaderManager.updateLanguage()
    }

    /**
     * app退出
     */
    fun appExit() {
        ResourceManager.clean()
        UserController.clearData()
    }

    /**
     * 用户登出
     */
    fun userLogout() {
        iIMCoreCallBack?.userLogout()
    }

    /**
     * 用户登入
     */
    fun userLogin() {
        iIMCoreCallBack?.userLogin()
    }

    fun updateGlobalDataConfig(globalConfig: GlobalConfigEntity) {
        this.globalConfig = globalConfig
        SPUtils.put(AppContext.context, Values.keyGlobalDataConfig, globalConfig.toJsonString())
    }

    fun getGlobalDataConfig(): GlobalConfigEntity {
        if (globalConfig != null && globalConfig.html != null) {
            return globalConfig
        }
        var value = SPUtils.getString(AppContext.context, Values.keyGlobalDataConfig, "")
        value?.takeIf { !it.isNullOrEmpty() }?.run {
            globalConfig = value.jsonToObj<GlobalConfigEntity>() ?: GlobalConfigEntity()
            globalConfig
        }?.run {
            globalConfig = this
        }
        return globalConfig
    }

    /**
     * 获取聊天等级配置信息
     */
    fun getChatLvl(chatNum: Int): Int {
        val level: ChatLvl = globalConfig?.chatLvl ?: ChatLvl()
        return when {
            chatNum >= level.lvl3 -> 3
            chatNum >= level.lvl2 -> 2
            chatNum >= level.lvl1 -> 1
            else -> 0
        }
    }

    /**
     * 获取用户平台解锁等级配置信息
     */
    fun getUnlockLvl(unlockNum: Int): Int {
        val level: UnlockLvl = globalConfig?.unlockLvl ?: UnlockLvl()
        return when {
            unlockNum >= level.lvl3 -> R.drawable.ic_unlock_lvl_3
            unlockNum >= level.lvl2 -> R.drawable.ic_unlock_lvl_2
            unlockNum >= level.lvl1 -> R.drawable.ic_unlock_lvl_1
            else -> 0
        }
    }

    fun getReportList(): ArrayList<AppReportItemEntity> {
        return globalConfig?.ReportList?.takeIf { !it.isNullOrEmpty() }?.run {
            this
        } ?: ArrayList<AppReportItemEntity>().also {
            it.add(AppReportItemEntity().also { item ->
                item.id = 1
                item.title = "looks like scam or fake profile"
            })

            it.add(AppReportItemEntity().also { item ->
                item.id = 1
                item.title = "looks like scam or fake profile"
            })

            it.add(AppReportItemEntity().also { item ->
                item.id = 2
                item.title = "Inappropriate content"
            })

            it.add(AppReportItemEntity().also { item ->
                item.id = 3
                item.title = "Others"
            })
        }
    }

    fun hasAgreedPrivacyAgreement(): Boolean {
        return true//SPUtils.getInt(AppContext.context, SPConstants.KEY_AGREED_P_AGREEMENT) > 0
    }

    fun getPushAccount(): String {
        return globalConfig?.sysAccount?.takeIf { !it.pushAccount.isNullOrEmpty() }?.run {
            pushAccount
        } ?: "20002"
    }

    fun getNoticeAccount(): String {
        return globalConfig?.sysAccount?.takeIf { !it.pushAccount.isNullOrEmpty() }?.run {
            this.noticeAccount
        } ?: "20001"
    }

    fun getQualityAccount(): String {
        return globalConfig?.sysAccount?.takeIf { !it.qualityAccount.isNullOrEmpty() }?.run {
            this.qualityAccount
        } ?: ""
    }

    fun getQAAccount(): String {
        return globalConfig?.sysAccount?.takeIf { !it.qaAccount.isNullOrEmpty() }?.run {
            this.qaAccount
        } ?: ""
    }

    fun getSalaryAccount(): String {
        return globalConfig?.sysAccount?.takeIf { !it.salaryAccount.isNullOrEmpty() }?.run {
            this.salaryAccount
        } ?: ""
    }

    fun getActiveTimeInternal(): Long {
        return globalConfig?.active_internal ?: 0
    }

    /**
     * 轮询的查询用户在线状态的间隔
     * 计算成ms，后台返的s
     */
    fun onlineQueryInterval(): Long {
        globalConfig?.also {
            if (it.onlineQueryInterval > 0) {
                return it.onlineQueryInterval.times(1000L)
            }
        }
        return GlobalConfigEntity.QUERY_MIN.times(1000L)
    }

    /**
     * 活跃状态的显示时长
     * 计算成ms，后台返的s
     */
    fun offlineActiveInterval(): Long {
        globalConfig?.also {
            if (it.offlineActiveInternal > 0) {
                return it.offlineActiveInternal.times(1000L)
            }
        }
        return (GlobalConfigEntity.ACTIVE_MIN).times(1000L)
    }

    /**
     * 获取上线通知间隔时间
     * 计算成ms，后台返的s
     */
    fun getOnlineNoticeInternal(): Long {
        globalConfig?.also {
            if (it.onlineNoticeInternal > 0) {
                return it.onlineNoticeInternal.times(1000L)
            }
        }
        return (GlobalConfigEntity.NOTICE_MIN).times(1000L)
    }

    /**
     * 每个model最大可置顶数，默认3个
     */
    fun getModelChatTopMax(): Int {
        return globalConfig?.modelChatTopMax ?: 3
    }

    /**
     * 会话列表每页的数据数，默认200
     */
    fun getConversationCountPerPage(): Int {
        return globalConfig?.conversationCountPerPage ?: 200
    }

    /**
     * 老用户召回入口展示 0：不展示 1：展示
     */
    fun showRecallVipBtn(): Int {
        return globalConfig?.showRecallVipBtn ?: 0
    }

    /**
     * 是否显示邀新入口
     */
    fun isShowInviteEntry(): Int {
        return globalConfig?.isShowInviteEntry ?: 0
    }

    fun isProdEnv(): Boolean {
        return DomainProvider.getEnv() == Values.env_product
    }

    fun isNeedShowUpdateRedDot(): Boolean {
        return appUpdateEntity?.takeIf { it.type != AppUpdateEntity.NO_UPDATE }?.run {
            true
        } ?: false
    }

    /**
     * 是否展示课程入口
     * @return true 显示
     */
    fun showTraining(): Boolean {
        return (globalConfig.config and 0x08) != 0
    }

    /**
     * 是否显示Google Map
     */
    fun showGoogleMap(): Boolean {
        return (globalConfig.config and 0x10) != 0
    }

    //表示开启私密消息(单和打包)的撤回
    fun isPrivateMsgRecall(): Boolean {
        val config = globalConfig?.config ?: 0
        return (config and 0x20) > 0
    }

    //表示开启非私密消息(文字，普通图片，视频，公开图片等)的撤回
    fun isPublicMsgRecall(): Boolean {
        val config = globalConfig?.config ?: 0
        return (config and 0x40) > 0
    }

    //用于设置SayHi过的数据显示位置，config有0x80，显示在chats中
    fun isChatShowMatch(): Boolean {
        val config = globalConfig?.config ?: 0
        return (config and 0x80) > 0
    }

    //用于设置用户平台解锁数量等级
    fun showUnlockLvl(): Boolean {
        val config = globalConfig?.config ?: 0
        return (config and 0x100) > 0
    }

    // 新用户是否显示聊天页招呼语按钮
    fun showNewUserGreet(): Boolean {
        val config = globalConfig?.config ?: 0
        return (config and 0x200) > 0
    }

    // 老用户是否显示聊天页招呼语按钮
    fun showOldUserGreet(): Boolean {
        val config = globalConfig?.config ?: 0
        return (config and 0x400) > 0
    }

    // 是否显示聊天页emoji按钮
    fun showMsgEmoji(): Boolean {
        val config = globalConfig?.config ?: 0
        return (config and 0x2000) > 0
    }

    // 是否显示Match流量开关
    fun isShowMatchTraffic(): Boolean {
        val config = globalConfig?.config ?: 0
        return (config and 0x4000) > 0
    }

    // 是否开启未读数显示错误的检测上报功能，默认开启
    fun isOpenUnreadError(): Boolean {
        val config = globalConfig?.config ?: 0
        return (config and 0x8000) > 0
    }
}