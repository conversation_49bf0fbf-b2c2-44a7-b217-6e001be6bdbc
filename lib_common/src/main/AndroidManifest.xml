<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <queries>
        <!-- for android 11 to adapt package visibility -->
        <package android:name="com.google.android.gms" />
        <intent>
            <action android:name="com.android.camera.action.CROP" />

            <data
                android:mimeType="image/*"
                android:scheme="content" />
        </intent>
    </queries>

    <!--  用于Android 11以上获取app列表  -->
    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent>
    </queries>

    <application android:supportsRtl="true">
        <activity
            android:name=".web.WebActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />

        <activity
            android:name=".image.AlbumChooseActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name=".image.ImgPreviewActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".video.VideoPlayActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <receiver
            android:name=".PhoneReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.PHONE_STATE" />
                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
                <action android:name="android.intent.action.NEW_OUTGOING_CALL" />
            </intent-filter>
        </receiver>

    </application>

</manifest>