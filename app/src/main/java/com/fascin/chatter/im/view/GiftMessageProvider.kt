package com.fascin.chatter.im.view

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.view.View
import android.view.ViewGroup
import com.fascin.chatter.R
import com.fascin.chatter.bean.chat.GiftEntity
import com.fascin.chatter.bean.event.GiftOpenEvent
import com.fascin.chatter.databinding.ItemChatGiftBinding
import com.fascin.chatter.im.msg.GiftMessage
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.MessageContent

class GiftMessageProvider : BaseMessageItemProvider<GiftMessage>() {
    override fun onCreateMessageContentViewHolder(
        parent: ViewGroup?,
        viewType: Int
    ): ViewHolder? {
        val view = View.inflate(parent?.context, R.layout.item_chat_gift, null)
        val binding = ItemChatGiftBinding.bind(view)
        view.tag = binding
        return ViewHolder(parent?.context, view)
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder?,
        parentHolder: ViewHolder?,
        t: GiftMessage?,
        uiMessage: UiMessage?,
        position: Int,
        list: List<UiMessage?>?,
        listener: IViewProviderListener<UiMessage?>?
    ) {
        holder?.convertView?.run {
            val binding = tag as ItemChatGiftBinding
            binding.gift.bindData(uiMessage)
        }
    }

    override fun onItemClick(
        holder: ViewHolder?,
        giftMessage: GiftMessage?,
        uiMessage: UiMessage?,
        position: Int,
        list: List<UiMessage?>?,
        listener: IViewProviderListener<UiMessage?>?
    ): Boolean {
        val data = giftMessage?.content?.jsonToObj<GiftEntity>() ?: return false
        val isSender = isGiftSender(uiMessage)
        val isOpened = isGiftOpened(uiMessage)

        when {
            isSender || isOpened -> { //预览播放礼物
                TODO("礼物播放预览")
            }
            else -> { //打开礼物
                SimpleRxBus.post(
                    GiftOpenEvent(uiMessage?.message?.uId.orEmpty(), data.originalMsgId.orEmpty())
                )
            }
        }
        return true
    }

    override fun isMessageViewType(messageContent: MessageContent?): Boolean {
        return messageContent is GiftMessage
    }

    override fun getSummarySpannable(
        context: Context?,
        t: GiftMessage?
    ): Spannable? {
        return SpannableString("[Gift]")
    }

    /**
     * 获取消息状态 0 未打开  1已打开
     */
    private fun isGiftOpened(uiMessage: UiMessage?): Boolean {
        var status = 0

        if (uiMessage?.expansion != null) {
            for ((k, v) in uiMessage.expansion) {
                if (k == "status") {
                    status = v.toIntOrNull() ?: 0
                }
            }
        }
        return status == 1
    }

    /**
     * 是否是消息发送方
     */
    private fun isGiftSender(uiMessage: UiMessage?): Boolean {
        val id = uiMessage?.message?.senderUserId ?: return false
        return UserController.isMe(id)
    }
}