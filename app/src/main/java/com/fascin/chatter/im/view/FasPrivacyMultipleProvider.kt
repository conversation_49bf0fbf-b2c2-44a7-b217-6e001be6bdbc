package com.fascin.chatter.im.view

import android.content.Context
import android.content.Intent
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.IMMultipleAlbumMsgData
import com.fascin.chatter.bean.event.PrivacyCountDownEvent
import com.fascin.chatter.bean.event.PrivacyLookEvent
import com.fascin.chatter.config.Config
import com.fascin.chatter.im.adapter.MultipleMsgAdapter
import com.fascin.chatter.im.msg.FasPrivacyMultipleAlbumMsg
import com.fascin.chatter.im.preview.MultiplePreviewActivity
import com.iandroid.allclass.lib_basecore.view.recyclerview.GridItemDecoration
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.beans.MediaEntity
import com.iandroid.allclass.lib_common.beans.MultipleMediaIntentEntity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toLongEx
import com.iandroid.allclass.lib_common.utils.timer.IActionListener
import com.iandroid.allclass.lib_common.utils.timer.ICountDownListener
import com.iandroid.allclass.lib_common.utils.timer.TAG_RV_ITEM
import io.rong.common.RLog
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.MessageClickType
import io.rong.imkit.model.UiMessage
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import kotlinx.android.synthetic.main.item_chat_privacy_multiple.view.ll_rc_privacy
import kotlinx.android.synthetic.main.item_chat_privacy_multiple.view.multipleMsgRv
import kotlinx.android.synthetic.main.item_chat_privacy_multiple.view.rcTimer
import kotlinx.android.synthetic.main.item_chat_privacy_multiple.view.rc_privacy_text
import kotlinx.android.synthetic.main.item_chat_privacy_multiple.view.rc_text
import kotlinx.android.synthetic.main.item_chat_privacy_multiple.view.rl_content
import kotlinx.android.synthetic.main.item_chat_privacy_multiple.view.threePic

/**
 * @Desc: 打包发送消息item类型
 * @Created: Quan
 * @Date: 2023/11/21
 */
class FasPrivacyMultipleProvider : BaseMessageItemProvider<FasPrivacyMultipleAlbumMsg>() {

    private val TAG = "FasPrivacyMultipleProvider"

    init {
        mConfig.showContentBubble = false
        mConfig.showProgress = false
        mConfig.showReadState = false
    }

    private var actionListener: IActionListener? = null
    private var providerListener: IViewProviderListener<UiMessage>? = null
    private var countDownCallbackMap: HashMap<Int, ICountDownListener>? = null
    private var itemImageAdapterMap: HashMap<Int, MultipleMsgAdapter> = HashMap()

    override fun initUserInfo(
        holder: ViewHolder?,
        uiMessage: UiMessage?,
        position: Int,
        listener: IViewProviderListener<UiMessage>?,
        isSender: Boolean
    ) {
        this.providerListener = listener
        // 是否显示头像
        mConfig.showPortrait = !privacyIsDestroyed(getPrivacyMsgStatus(uiMessage))
        super.initUserInfo(holder, uiMessage, position, listener, isSender)
    }

    override fun initContent(
        holder: ViewHolder?,
        isSender: Boolean,
        uiMessage: UiMessage?,
        position: Int,
        listener: IViewProviderListener<UiMessage>?,
        list: MutableList<UiMessage>?
    ) {
        // 消息内容是否居中显示
        mConfig.centerInHorizontal = privacyIsDestroyed(getPrivacyMsgStatus(uiMessage))
        super.initContent(holder, isSender, uiMessage, position, listener, list)
    }

    override fun onCreateMessageContentViewHolder(parent: ViewGroup?, viewType: Int): ViewHolder {
        val view: View =
            LayoutInflater.from(parent?.context)
                .inflate(R.layout.item_chat_privacy_multiple, parent, false)
        return ViewHolder(view.context, view)
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder?,
        parentHolder: ViewHolder?,
        message: FasPrivacyMultipleAlbumMsg?,
        uiMessage: UiMessage,
        position: Int,
        list: MutableList<UiMessage>?,
        listener: IViewProviderListener<UiMessage>?
    ) {
        holder?.convertView?.run {
            if (!checkViewsValid(multipleMsgRv)) {
                RLog.e(TAG, "checkViewsValid error," + uiMessage.objectName)
                return
            }

            message?.content?.jsonToObj<IMMultipleAlbumMsgData>()?.also {
                val status = getPrivacyMsgStatus(uiMessage)
                if (privacyIsCanLook(status)) {
                    // 更正 消息在open状态下断网后，本地消息无法更新时的UI设置
                    if (getPrivacyDeadTime(uiMessage) <= 0) {
                        setDestroyedViewUI(holder, uiMessage)
                        return
                    }
                    if (it.mediaEntitys.size == 3) {
                        setThreePicUI(this, true, it, uiMessage)
                    } else {
                        setMultipleRv(this, true, it, uiMessage)
                    }
                    rcTimer.show(true)
                    ll_rc_privacy.show(false)
                    rl_content.show(true)
                    rc_text.show(false)
                    // 配置倒计时监听
                    val countDownCallBack = getCountDownCallBack(holder, uiMessage)
                    actionListener?.getCountDownTimer(TAG_RV_ITEM)?.showCountDownTimer(
                        getPrivacyDeadTime(uiMessage), countDownCallBack
                    )
                } else if (privacyIsDestroyed(status)) {
                    setDestroyedViewUI(holder, uiMessage)
                } else {
                    if (it.mediaEntitys.size == 3) {
                        setThreePicUI(this, false, it, uiMessage)
                    } else {
                        setMultipleRv(this, false, it, uiMessage)
                    }
                    rcTimer.show(false)
                    ll_rc_privacy.show(true)
                    rl_content.show(true)
                    rc_text.show(false)
                    rc_privacy_text.text = String.format(
                        context.getString(
                            if (it.type == Config.MediaVideo) R.string.privacy_tag_videos
                            else R.string.privacy_tag_photos
                        ),
                        it.mediaEntitys.size
                    )
                }
            }
        }
    }

    override fun onItemClick(
        holder: ViewHolder?,
        multipleMessage: FasPrivacyMultipleAlbumMsg?,
        uiMessage: UiMessage?,
        position: Int,
        list: MutableList<UiMessage>?,
        listener: IViewProviderListener<UiMessage>?
    ): Boolean {
        return false
    }

    /**
     * 3张时的UI（特殊处理）
     */
    private fun setThreePicUI(
        itemView: View?,
        locked: Boolean,
        msgData: IMMultipleAlbumMsgData,
        uiMessage: UiMessage
    ) {
        itemView?.run {
            multipleMsgRv.show(false)
            threePic.show(true)
            threePic.setData(msgData.mediaEntitys, locked)
            threePic.setItemClickListener(object : ParticularPicVIew.OnItemClickListener {
                override fun onItemClick(id: Long?, position: Int) {
                    picClick(msgData, uiMessage, position)
                }

                override fun onItemLongClick(position: Int) {
                    providerListener?.onViewLongClick(
                        MessageClickType.CONTENT_LONG_CLICK,
                        uiMessage
                    )
                }
            })
        }
    }

    /**
     * 不同数量展示不同的UI
     */
    private fun setMultipleRv(
        itemView: View?,
        locked: Boolean,
        msgData: IMMultipleAlbumMsgData,
        uiMessage: UiMessage
    ) {
        itemView?.run {
            multipleMsgRv.show(true)
            threePic.show(false)
            val imageAdapter = getItemImageAdapter(uiMessage.messageId, this)
            val medias = msgData.mediaEntitys
            multipleMsgRv.layoutManager = GridLayoutManager(context, 6).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        when (medias.size) {
                            2, 3, 4 -> {
                                return 3
                            }

                            5 -> {
                                return if (position < 2) 3 else 2
                            }

                            6 -> {
                                return 2
                            }

                            7 -> {
                                return if (position < 4) 3 else 2
                            }

                            8 -> {
                                return if (position < 2) 3 else 2
                            }

                            9 -> {
                                return 2
                            }
                        }
                        return 1
                    }
                }
            }
            multipleMsgRv.adapter = imageAdapter
            imageAdapter?.setShowBlur(!locked)
            imageAdapter?.updateData(medias)
            imageAdapter?.setItemClickListener(object : MultipleMsgAdapter.OnItemClickListener {
                override fun onItemClick(id: Long, position: Int) {
                    picClick(msgData, uiMessage, position)
                }

                override fun onItemLongClick(position: Int) {
                    providerListener?.onViewLongClick(
                        MessageClickType.CONTENT_LONG_CLICK,
                        uiMessage
                    )
                }
            })
        }
    }

    private fun getItemImageAdapter(
        msgId: Int,
        itemView: View?
    ): MultipleMsgAdapter? {
        if (itemImageAdapterMap[msgId] == null) {
            val adapter = MultipleMsgAdapter()
            itemImageAdapterMap[msgId] = adapter
            if (itemView?.multipleMsgRv?.itemDecorationCount!! <= 0) {
                itemView.multipleMsgRv?.addItemDecoration(
                    GridItemDecoration.Builder(AppContext.context)
                        .setColorResource(R.color.white)
                        .setHorizontalSpan(1f)
                        .setVerticalSpan(2f)
                        .build()
                )
            }
        }
        return itemImageAdapterMap[msgId]
    }

    /**
     * 给对应holder创建回调
     */
    private fun getCountDownCallBack(
        holder: ViewHolder?,
        uiMessage: UiMessage
    ): ICountDownListener {
        if (countDownCallbackMap == null)
            countDownCallbackMap = HashMap()

        val call = object : ICountDownListener {
            override fun onCountDownTick(
                day: Long,
                hour: Long,
                minute: Long,
                second: Long
            ) {
                val timeStr = "${getDouble(hour)}:${getDouble(minute)}:${getDouble(second)}"
                // 通知播放页更新
                SimpleRxBus.post(PrivacyCountDownEvent(uiMessage.messageId, timeStr, false))
                // 每秒更新一次
                holder?.convertView?.rcTimer?.text = timeStr
            }

            override fun onCountDownFinish() {
                // 倒计时结束
                setDestroyedViewUI(holder, uiMessage)
                //清除对应监听
                clearCallByMessageId(uiMessage.messageId)
                // 通知播放页停止展示
                SimpleRxBus.post(PrivacyCountDownEvent(uiMessage.messageId, "", true))
            }
        }

        clearCallByMessageId(uiMessage.messageId)
        countDownCallbackMap?.put(uiMessage.messageId, call)
        // 注册回调
        actionListener?.getCountDownTimer(TAG_RV_ITEM)?.register(
            call,
            getPrivacyDeadTime(uiMessage)
        )
        return call
    }

    private fun picClick(
        msgData: IMMultipleAlbumMsgData,
        uiMessage: UiMessage,
        position: Int
    ) {
        val status = getPrivacyMsgStatus(uiMessage)
        if (privacyIsDestroyed(status)) return
        if (!privacyIsCanLook(status)
            && !UserController.isMe(uiMessage.message?.senderUserId.orEmpty())
        ) {
            SimpleRxBus.post(
                // 打包发送资源id传-1
                PrivacyLookEvent(
                    uiMessage.message?.uId!!, msgData.msgId, -1
                )
            )
        } else {
            val intent = Intent(AppContext.getTopActivity(), MultiplePreviewActivity::class.java)
            intent.putExtra("msgMediaData", MultipleMediaIntentEntity().also {
                it.msgId = uiMessage.messageId
                it.mediaList = msgData.mediaEntitys
                it.type = msgData.type
                it.sendType = MediaEntity.SendTypePrivate
            })
            intent.putExtra("position", position)
            intent.putExtra(
                "isCountDown",
                getPrivacyDeadTime(uiMessage) <= 0 || privacyIsDestroyed(status)
            )
            AppContext.getTopActivity()?.startActivity(intent)
        }
    }

    /**
     * 设置私密消息已销毁时的UI
     */
    private fun setDestroyedViewUI(holder: ViewHolder?, uiMessage: UiMessage) {
        holder?.convertView?.run {
            rl_content.show(false)
            rc_text.show(true)
            uiMessage.message?.let {
                (it.content as FasPrivacyMultipleAlbumMsg).content.orEmpty()
                    .jsonToObj<IMMultipleAlbumMsgData>()
                    ?.also { multipleMsgData ->
                        if (it.messageDirection == Message.MessageDirection.SEND) {
                            rc_text.text =
                                context.getString(
                                    if (multipleMsgData.type == Config.MediaImage) R.string.privacy_pack_photo_destroyed
                                    else R.string.privacy_pack_video_destroyed
                                )
                        } else {
                            rc_text.text = String.format(
                                context.getString(
                                    if (multipleMsgData.type == Config.MediaImage) R.string.privacy_pack_photo_destroyed_re
                                    else R.string.privacy_pack_video_destroyed_re
                                ),
                                uiMessage.userInfo.name
                            )
                        }
                    }
            }
        }
    }

    /**
     * 清楚指定message对应的回调
     */
    private fun clearCallByMessageId(messageId: Int) {
        if (countDownCallbackMap?.get(messageId) != null) {
            actionListener?.getCountDownTimer(TAG_RV_ITEM)
                ?.unregister(countDownCallbackMap?.get(messageId)!!)
            countDownCallbackMap?.remove(messageId)
        }
    }

    /**
     * 页面销毁，置空变量
     */
    override fun onViewDestroy() {
        actionListener = null
        providerListener = null
        countDownCallbackMap?.clear()
        itemImageAdapterMap.clear()
    }

    /**
     * 获取消息状态 0 未打开  1已打开  2 已销毁
     */
    private fun getPrivacyMsgStatus(uiMessage: UiMessage?): Long {
        if (uiMessage?.expansion != null) {
            for ((k, v) in uiMessage.expansion) {
                if (k == "status") {
                    return v.toLongEx(0)
                }
            }
        }

        return 0
    }

    /**
     * 计算剩余倒计时
     */
    private fun getPrivacyDeadTime(uiMessage: UiMessage): Long {
        if (uiMessage.expansion != null) {
            for ((k, v) in uiMessage.expansion) {
                if (k == "del_time") {
                    val countdown = v.toLongEx(0) * 1000 - System.currentTimeMillis()
                    return if (countdown > 0) countdown else 0
                }
            }
        }
        return 0
    }

    private fun privacyIsDestroyed(status: Long): Boolean {
        return status == 2L
    }

    private fun privacyIsCanLook(status: Long): Boolean {
        return status == 1L
    }

    private fun senderIsMe(uiMessage: UiMessage?): Boolean {
        return uiMessage?.run {
            UserController.isMe(message?.senderUserId.orEmpty())
        } ?: false
    }

    private fun getDouble(value: Long): String {
        return if (value < 10) "0$value" else value.toString()
    }

    override fun setActionListener(actionListener: IActionListener?) {
        this.actionListener = actionListener
    }

    override fun getSummarySpannable(
        context: Context?,
        imageMessage: FasPrivacyMultipleAlbumMsg?
    ): Spannable {
        imageMessage?.content?.jsonToObj<IMMultipleAlbumMsgData>()?.also {
            if (it.type == Config.MediaImage) {//私密图片
                return SpannableString(context?.getString(R.string.privacy_images)
                    ?.let { str -> String.format(str, it.mediaEntitys.size) }
                )
            } else if (it.type == Config.MediaVideo) {//私密视频
                return SpannableString(context?.getString(R.string.privacy_videos)
                    ?.let { str -> String.format(str, it.mediaEntitys.size) }
                )
            }
        }
        return SpannableString(
            context?.getString(R.string.privacy_file)
        )
    }

    override fun isMessageViewType(messageContent: MessageContent?): Boolean {
        return messageContent is FasPrivacyMultipleAlbumMsg && !messageContent.isDestruct()
    }

}