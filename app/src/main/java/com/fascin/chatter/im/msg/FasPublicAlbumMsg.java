package com.fascin.chatter.im.msg;

import android.os.Parcel;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;

import io.rong.common.ParcelUtils;
import io.rong.imlib.MessageTag;
import io.rong.message.MediaMessageContent;

/**
 * chatter发送的视频消息，以及fascin8发送过来的SightMsg
 * （融云小视频消息SightMsg，服务器拦截转发时，转成了connectfriends:public）
 */
@MessageTag(value = "connectfriends:public", flag = MessageTag.ISCOUNTED)
public class FasPublicAlbumMsg extends MediaMessageContent {

    private static final String TAG = "SuConnectMsg";
    // 自定义消息变量，可以有多个
    private String content;

    private FasPublicAlbumMsg() {
    }

    /**
     * 设置文字消息的内容。
     *
     * @param content 文字消息的内容。
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 构造函数。
     *
     * @param in 初始化传入的 Parcel。
     */
    public FasPublicAlbumMsg(Parcel in) {
        setExtra(ParcelUtils.readFromParcel(in));
        setContent(ParcelUtils.readFromParcel(in));
    }

    // 快速构建消息对象方法
    public static FasPublicAlbumMsg obtain(String content) {
        FasPublicAlbumMsg msg = new FasPublicAlbumMsg();
        msg.content = content;
        return msg;
    }

    /**
     * 创建 MyTextContent(byte[] data) 带有 byte[] 的构造方法用于解析消息内容.
     */
    public FasPublicAlbumMsg(byte[] data) {
        if (data == null) {
            return;
        }
        String jsonStr = null;
        try {
            jsonStr = new String(data, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException ", e);
        }
        if (jsonStr == null) {
            Log.e(TAG, "jsonStr is null ");
            return;
        }

        try {
            JSONObject jsonObj = new JSONObject(jsonStr);
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (jsonObj.has("user")) {
                setUserInfo(parseJsonToUserInfo(jsonObj.getJSONObject("user")));
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (jsonObj.has("mentionedInfo")) {
                setMentionedInfo(parseJsonToMentionInfo(jsonObj.getJSONObject("mentionedInfo")));
            }
            // 将所有自定义变量从收到的 json 解析并赋值
            if (jsonObj.has("content")) {
                content = jsonObj.optString("content");
                Log.e(TAG, "content: " + content);

            }
        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }
    }

    public String getContent() {
        return content;
    }

    /**
     * 将本地消息对象序列化为消息数据。
     *
     * @return 消息数据。
     */
    @Override
    public byte[] encode() {
        JSONObject jsonObj = new JSONObject();
        try {
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (getJSONUserInfo() != null) {
                jsonObj.putOpt("user", getJSONUserInfo());
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (getJsonMentionInfo() != null) {
                jsonObj.putOpt("mentionedInfo", getJsonMentionInfo());
            }
            //  将所有自定义消息的内容，都序列化至 json 对象中
            jsonObj.put("content", this.content);
        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }

        try {
            return jsonObj.toString().getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException ", e);
        }
        return null;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int i) {
        // 对消息属性进行序列化，将类的数据写入外部提供的 Parcel 中
        ParcelUtils.writeToParcel(dest, getExtra());
        ParcelUtils.writeToParcel(dest, getContent());
    }

    public static final Creator<FasPublicAlbumMsg> CREATOR =
            new Creator<FasPublicAlbumMsg>() {
                @Override
                public FasPublicAlbumMsg createFromParcel(Parcel source) {
                    return new FasPublicAlbumMsg(source);
                }

                @Override
                public FasPublicAlbumMsg[] newArray(int size) {
                    return new FasPublicAlbumMsg[size];
                }
            };
}
