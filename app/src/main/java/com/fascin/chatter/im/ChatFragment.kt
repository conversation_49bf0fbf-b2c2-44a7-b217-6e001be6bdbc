package com.fascin.chatter.im

import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.text.TextUtils
import android.util.SparseArray
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.core.util.forEach
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.addEmojiID
import com.fascin.chatter.bean.IMChatIntent
import com.fascin.chatter.bean.ImMsgAction
import com.fascin.chatter.bean.PrivacyListIntent
import com.fascin.chatter.bean.chat.UserImExtraEntity
import com.fascin.chatter.bean.event.EmojiSelectedEvent
import com.fascin.chatter.bean.event.GiftOpenEvent
import com.fascin.chatter.bean.event.GreetSelectedEvent
import com.fascin.chatter.bean.event.PrivacyLookEvent
import com.fascin.chatter.im.extension.FasItemMessageLongClick
import com.fascin.chatter.im.view.FasExtension
import com.fascin.chatter.im.view.IMRefreshHeader
import com.fascin.chatter.main.profile.ProfileViewModel
import com.fascin.chatter.main.viewmodel.GiftViewModel
import com.fascin.chatter.pay.PrivacyListActivity
import com.fascin.chatter.repository.AppRepository
import com.fascin.chatter.utils.ConnectFlagInvisible
import com.fascin.chatter.utils.ConnectFlagProhibition
import com.fascin.chatter.utils.isFlagEnabled
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.bean.ActionEntity
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import com.iandroid.allclass.lib_common.utils.timer.CurCountDownTimer
import com.iandroid.allclass.lib_common.utils.timer.IActionListener
import io.rong.common.RLog
import io.rong.imkit.IMCenter
import io.rong.imkit.MessageItemLongClickAction
import io.rong.imkit.MessageItemLongClickActionManager
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversation.BaseConversationFragment
import io.rong.imkit.conversation.ConversationFragment
import io.rong.imkit.conversation.MessageListAdapter
import io.rong.imkit.conversation.extension.InputMode
import io.rong.imkit.conversation.extension.RongExtensionViewModel
import io.rong.imkit.conversation.messgelist.processor.IConversationUIRenderer
import io.rong.imkit.conversation.messgelist.status.MessageProcessor.GetMessageCallback
import io.rong.imkit.conversation.messgelist.viewmodel.MessageViewModel
import io.rong.imkit.event.Event
import io.rong.imkit.event.uievent.PageDestroyEvent
import io.rong.imkit.event.uievent.PageEvent
import io.rong.imkit.event.uievent.ScrollEvent
import io.rong.imkit.event.uievent.ScrollMentionEvent
import io.rong.imkit.event.uievent.ScrollToEndEvent
import io.rong.imkit.event.uievent.ShowLoadMessageDialogEvent
import io.rong.imkit.event.uievent.ShowLongClickDialogEvent
import io.rong.imkit.event.uievent.ShowWarningDialogEvent
import io.rong.imkit.event.uievent.SmoothScrollEvent
import io.rong.imkit.event.uievent.ToastEvent
import io.rong.imkit.feature.location.LocationUiRender
import io.rong.imkit.feature.reference.ReferenceManager
import io.rong.imkit.manager.MessageProviderPermissionHandler
import io.rong.imkit.manager.hqvoicemessage.HQVoiceMsgDownloadManager
import io.rong.imkit.model.UiMessage
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.utils.PermissionCheckUtil
import io.rong.imkit.utils.RongViewUtils
import io.rong.imkit.utils.RouteUtils
import io.rong.imkit.widget.FixedLinearLayoutManager
import io.rong.imkit.widget.adapter.BaseAdapter
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imkit.widget.dialog.OptionsPopupDialog
import io.rong.imkit.widget.refresh.api.RefreshLayout
import io.rong.imkit.widget.refresh.constant.RefreshState
import io.rong.imkit.widget.refresh.listener.OnLoadMoreListener
import io.rong.imkit.widget.refresh.listener.OnRefreshListener
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.ConversationIdentifier
import io.rong.imlib.model.Message
import kotlinx.android.synthetic.main.fragment_chat.mList
import kotlinx.android.synthetic.main.fragment_chat.mNewMessageNum
import kotlinx.android.synthetic.main.fragment_chat.mRefreshLayout
import kotlinx.android.synthetic.main.fragment_chat.mRongExtension
import kotlinx.android.synthetic.main.fragment_chat.mUnreadHistoryMessageNum
import kotlinx.android.synthetic.main.fragment_chat.mUnreadMentionMessageNum
import kotlinx.android.synthetic.main.fragment_chat.rootView
import java.text.MessageFormat

class ChatFragment : BaseConversationFragment(R.layout.fragment_chat), OnRefreshListener,
    View.OnClickListener, OnLoadMoreListener, IActionListener, IViewProviderListener<UiMessage> {
    /** 开启合并转发的选择会话界面  */
    val REQUEST_CODE_FORWARD = 104

    private val REQUEST_MSG_DOWNLOAD_PERMISSION = 1000
    private val TAG = ConversationFragment::class.java.simpleName
    protected var mLinearLayoutManager: RecyclerView.LayoutManager? = null
    protected var mAdapter: MessageListAdapter? = null

    protected var mMessageViewModel: MessageViewModel? = null
    private val mGiftViewModel: GiftViewModel by viewModels()
    protected var mRongExtensionViewModel: RongExtensionViewModel? = null
    protected var activitySoftInputMode = 0

    // 计时器容器
    private val countDownMap = SparseArray<CurCountDownTimer?>()

    private val viewModel by lazy {
        ViewModelProvider(
            this, ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }

    // 滑动结束是否
    protected var onScrollStopRefreshList = false
    private var bindToConversation = false
    var mListObserver = Observer { uiMessages: List<UiMessage> ->
        refreshList(
            uiMessages
        )
    }
    var mNewMessageUnreadObserver: Observer<Int> = Observer { count ->
        if (RongConfigCenter.conversationConfig()
                .isShowNewMessageBar(mMessageViewModel!!.curConversationType)
        ) {
            if (count != null && count > 0) {
                mNewMessageNum.visibility = View.VISIBLE
                mNewMessageNum.text = if (count > 99) "99+" else count.toString()
            } else {
                mNewMessageNum.visibility = View.INVISIBLE
            }
        }
    }
    var mHistoryMessageUnreadObserver: Observer<Int> = Observer { count ->
        if (RongConfigCenter.conversationConfig()
                .isShowHistoryMessageBar(mMessageViewModel!!.curConversationType)
        ) {
            if (count != null && count > 0) {
                mUnreadHistoryMessageNum.visibility = View.VISIBLE
                mUnreadHistoryMessageNum.text = MessageFormat.format(
                    getString(io.rong.imkit.R.string.rc_unread_message),
                    if (count > 99) "99+" else count
                )
            } else {
                mUnreadHistoryMessageNum!!.visibility = View.GONE
            }
        }
    }
    var mNewMentionMessageUnreadObserver: Observer<Int> = Observer { count ->
        if (RongConfigCenter.conversationConfig().isShowNewMentionMessageBar(
                mMessageViewModel!!.curConversationType
            )
        ) {
            if (count != null && count > 0) {
                mUnreadMentionMessageNum.visibility = View.VISIBLE
                mUnreadMentionMessageNum.text =
                    getString(io.rong.imkit.R.string.rc_mention_messages, "($count)")
            } else {
                mUnreadMentionMessageNum.visibility = View.GONE
            }
        }
    }
    var mPageObserver: Observer<PageEvent> = Observer { event ->
        // 优先透传给各模块的 view 处理中心进行处理，如果返回 true, 代表事件被消费，不再处理。
        for (processor: IConversationUIRenderer in RongConfigCenter.conversationConfig().viewProcessors) {
            if (processor.handlePageEvent(event)) {
                return@Observer
            }
        }
        if (event is Event.RefreshEvent) {
            if (event.state == RefreshState.RefreshFinish) {
                mRefreshLayout.finishRefresh()
            } else if (event.state == RefreshState.LoadFinish) {
                mRefreshLayout.finishLoadMore()
            }
        } else if (event is ToastEvent) {
            val msg = event.message
            if (!TextUtils.isEmpty(msg)) {
                ToastUtils.showToast(msg)
            }
        } else if (event is ScrollToEndEvent) {
            mList.scrollToPosition(mAdapter!!.itemCount - 1)
        } else if (event is ScrollMentionEvent) {
            mMessageViewModel!!.onScrolled(
                mList, 0, 0, mAdapter!!.headersCount, mAdapter!!.footersCount
            )
        } else if (event is ScrollEvent) {
            if (mList.layoutManager is LinearLayoutManager) {
                (mList.layoutManager as LinearLayoutManager?)?.scrollToPositionWithOffset(
                    mAdapter!!.headersCount + event.position, 0
                )
            }
        } else if (event is SmoothScrollEvent) {
            if (mList.layoutManager is LinearLayoutManager) {
                (mList.layoutManager as LinearLayoutManager?)?.scrollToPositionWithOffset(
                    (mAdapter!!.headersCount + event.position), 0
                )
            }
        } else if (event is ShowLongClickDialogEvent) {  //长按消息弹出菜单
            val bean = event.bean
            val messageItemLongClickActions: ArrayList<MessageItemLongClickAction> = ArrayList()

            //隐藏recall功能
            messageItemLongClickActions.add(FasItemMessageLongClick.messageItemRecallLongClickAction())

            val titles: MutableList<String> = ArrayList()
            for (action: MessageItemLongClickAction in messageItemLongClickActions) {
                titles.add(action.getTitle(context))
            }
            val dialog = OptionsPopupDialog.newInstance(
                context, titles.toTypedArray()
            ).setOptionsPopupDialogListener { which ->
                messageItemLongClickActions[which].listener.onMessageItemLongClick(
                    context, bean.uiMessage
                )
            }
            MessageItemLongClickActionManager.getInstance().longClickDialog = dialog
            MessageItemLongClickActionManager.getInstance().longClickMessage =
                bean.uiMessage.message
            dialog.setOnDismissListener {
                MessageItemLongClickActionManager.getInstance().longClickDialog = null
                MessageItemLongClickActionManager.getInstance().longClickMessage = null
            }
            if (FasItemMessageLongClick.isShowDialog(bean.uiMessage)) {
                dialog.show()
            }
        } else if (event is PageDestroyEvent) {
            val fm = childFragmentManager
            if (fm.backStackEntryCount > 0) {
                fm.popBackStack()
            } else {
                if (activity != null) {
                    activity!!.finish()
                }
            }
        } else if (event is ShowWarningDialogEvent) {
            onWarningDialog(event.message)
        } else if (event is ShowLoadMessageDialogEvent) {
            showLoadMessageDialog(
                event.callback, event.list
            )
        }
    }

    private var mNotificationContainer: LinearLayout? = null
    private var onViewCreated = false
    private var mDisableSystemEmoji = false
    private var mBundle: Bundle? = null
    private var conversationIdentifier: ConversationIdentifier? = null
    private val mScrollListener: RecyclerView.OnScrollListener =
        object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                mMessageViewModel?.onScrolled(
                    recyclerView, dx, dy, mAdapter!!.headersCount, mAdapter!!.footersCount
                )
            }

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE && onScrollStopRefreshList) {
                    onScrollStopRefreshList = false
                    RLog.d(TAG, "onScrollStateChanged refresh List")
                    refreshList(mMessageViewModel!!.uiMessageLiveData.value!!)
                }
            }
        }

    fun initConversation(
        targetId: String?, conversationType: Conversation.ConversationType?, bundle: Bundle?
    ) {
        if (onViewCreated) {
            bindConversation(
                ConversationIdentifier.obtain(conversationType, targetId, ""), false, bundle
            )
        } else {
            conversationIdentifier = ConversationIdentifier.obtain(conversationType, targetId, "")
            mBundle = bundle
        }
    }

    private fun bindConversation(
        conversationIdentifier: ConversationIdentifier?,
        disableSystemEmoji: Boolean,
        bundle: Bundle?
    ) {
        if (conversationIdentifier!!.type != null && !TextUtils.isEmpty(conversationIdentifier.targetId)) {
            mRongExtension?.bindToConversation(this, conversationIdentifier, disableSystemEmoji)
            userIsInVisible()
            mMessageViewModel?.bindConversation(conversationIdentifier, bundle)
            subscribeUi()
            bindToConversation = true
        } else {
            RLog.e(
                TAG, "Invalid intent data !!! Must put targetId and conversation type to intent."
            )
        }
    }

    fun getSuRongExtension(): FasExtension {
        return mRongExtension
    }

    private fun subscribeUi() {
        viewModel.compositeDisposable?.add(SimpleRxBus.observe(PrivacyLookEvent::class) {
            if (it.media_id == -1) {
                // 打包私密消息解锁
                viewModel.openPrivacyMultipleMsg(it.to_msgid, it.from_msgid)
            } else {
                viewModel.openPrivacyMsg(it.to_msgid, it.from_msgid, it.media_id)
            }
        })

        viewModel.compositeDisposable?.add(SimpleRxBus.observe(GiftOpenEvent::class) {
            // 打开礼物
            viewModel.openGiftMessage(it.toMsgId, it.fromMsgId)
        })

        viewModel.compositeDisposable?.add(SimpleRxBus.observe(GreetSelectedEvent::class) {
            // 设置招呼语到输入框
            mRongExtensionViewModel?.setMsgGreetData(it.greetId, it.content)
        })

        viewModel.compositeDisposable?.add(SimpleRxBus.observe(EmojiSelectedEvent::class) {
            // 设置emoji到输入框
            mRongExtensionViewModel?.setMsgEmojiData(it.content)
            it.emojiId.addEmojiID()
        })

        mMessageViewModel?.also {
            it.expansionLiveData.observe(viewLifecycleOwner) { expansionData ->
                expansionData.forEach { (key: String, value: String) ->
                    if (key == "data") {
                        value.jsonToObj<ImMsgAction>()?.also { msgData ->
                            context.routeAction(msgData.action)
                        }
                    }
                }
            }

            it.pageEventLiveData.observeForever(mPageObserver)
            it.uiMessageLiveData.observeForever(mListObserver)
            it.newMessageUnreadLiveData?.observe(
                viewLifecycleOwner, mNewMessageUnreadObserver
            )
            it.historyMessageUnreadLiveData?.observe(
                viewLifecycleOwner, mHistoryMessageUnreadObserver
            )
            it.newMentionMessageUnreadLiveData?.observe(
                viewLifecycleOwner, mNewMentionMessageUnreadObserver
            )
        }

        mRongExtensionViewModel?.extensionBoardState?.observe(
            viewLifecycleOwner
        ) { value ->
            RLog.d(TAG, "scroll to the bottom")
            mList?.postDelayed(
                {
                    val inputMode = mRongExtensionViewModel?.inputModeLiveData?.value
                    if ((inputMode != InputMode.MoreInputMode && (java.lang.Boolean.TRUE == value))) {
                        if (mMessageViewModel?.isNormalState == true) {
                            mList?.scrollToPosition(
                                mAdapter?.itemCount?.minus(1) ?: 0
                            )
                        } else if (mMessageViewModel?.isHistoryState == false) {
                            mMessageViewModel?.newMessageBarClick()
                        }
                    }
                }, 150
            )
        }

        // 弹出招呼语弹窗
        mRongExtensionViewModel?.expandClickLiveData?.observe(viewLifecycleOwner) {
            if (it.equals(InputMode.PublicMode)) {
                // 点击跳转公开照片页
                mRongExtension?.clickMediaPlugin(true)
            } else if (it.equals(InputMode.PrivateMode)) {
                // 点击跳转私密照片页
                mRongExtension?.clickMediaPlugin(false)
            } else if (it.equals(InputMode.CurEmojiMode)) {
                showCurEmojiDialog()
            } else if (it.equals(InputMode.MsgGreetMode)) {
                showGreetDialog()
            } else if (it.equals(InputMode.MoreGiftMode)) {
                showGiftDialog()
            }
            mRongExtensionViewModel?.inputModeLiveData?.postValue(InputMode.NormalMode)
        }

        //点击发送上报EmojiID埋点信息
        mRongExtensionViewModel?.expandClickSendLiveData?.observe(viewLifecycleOwner) {
            if (AppModule.selectEmojiId.isNotEmpty()) {
                AppRepository.eventTrace(EventKey.im_em_c_s) {
                    "targetId" to conversationIdentifier?.targetId
                    "emojiId" to AppModule.selectEmojiId
                }
                AppModule.selectEmojiId = ""
            }
        }
    }

    override fun onViewClick(clickType: Int, data: UiMessage?) {
        if (MessageProviderPermissionHandler.getInstance()
                .handleMessageClickPermission(data, this)
        ) {
            return
        }
        mMessageViewModel!!.onViewClick(clickType, data)
    }

    override fun onViewLongClick(clickType: Int, data: UiMessage?): Boolean {
        return mMessageViewModel!!.onViewLongClick(clickType, data)
    }

    /**
     * 计时器
     */
    override fun getCountDownTimer(tag: Int): CurCountDownTimer? {
        var countDownTimer = countDownMap.get(tag)
        if (countDownTimer == null) {
            countDownTimer = CurCountDownTimer()
            countDownTimer.startCountDown()
            countDownMap.put(tag, countDownTimer)
        }
        return countDownTimer
    }

    /**
     * 获取顶部通知栏容器
     *
     * @return 通知栏容器
     */
    fun getNotificationContainer(): LinearLayout? {
        return mNotificationContainer
    }

    /**
     * 隐藏调用showNotificationView所显示的通知view
     *
     * @param notificationView 通知栏 view
     */
    fun hideNotificationView(notificationView: View?) {
        if (notificationView == null) {
            return
        }
        val view = mNotificationContainer!!.findViewById<View>(notificationView.id)
        if (view != null) {
            mNotificationContainer!!.removeView(view)
            if (mNotificationContainer!!.childCount == 0) {
                mNotificationContainer!!.visibility = View.GONE
            }
        }
    }

    /** 在通知区域显示一个view  */
    fun showNotificationView(notificationView: View?) {
        if (notificationView == null) {
            return
        }
        mNotificationContainer!!.removeAllViews()
        RongViewUtils.addView(mNotificationContainer, notificationView)
        mNotificationContainer!!.visibility = View.VISIBLE
    }

    private fun refreshList(data: List<UiMessage>) {
        if (!mList!!.isComputingLayout && mList!!.scrollState == RecyclerView.SCROLL_STATE_IDLE) {
            mAdapter!!.setDataCollection(data)
        } else {
            onScrollStopRefreshList = true
        }
    }

    fun onBackPressed(): Boolean {
        var result = false
        for (processor in RongConfigCenter.conversationConfig().viewProcessors) {
            val temp = processor.onBackPressed()
            if (temp) {
                result = true
            }
        }
        if (mMessageViewModel != null) {
            val temp = mMessageViewModel!!.onBackPressed()
            if (temp) {
                result = true
            }
        }
        if (mRongExtensionViewModel != null) {
            mRongExtensionViewModel!!.exitMoreInputMode(context)
            mRongExtensionViewModel!!.collapseExtensionBoard()
        }
        return result
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        if (mMessageViewModel != null && bindToConversation) {
            mMessageViewModel!!.onRefresh()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            ReferenceManager.getInstance().hideReferenceView()
        }
        if (requestCode == REQUEST_CODE_FORWARD) {
            if (mMessageViewModel != null) mMessageViewModel!!.forwardMessage(data)
            return
        }
        mRongExtension?.onActivityPluginResult(requestCode, resultCode, data)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String?>, grantResults: IntArray
    ) {
        if (PermissionCheckUtil.checkPermissionResultIncompatible(permissions, grantResults)) {
            if (context != null) {
                ToastUtils.showToast(
                    getString(io.rong.imkit.R.string.rc_permission_request_failed)
                )
            }
            return
        }
        if (requestCode == REQUEST_MSG_DOWNLOAD_PERMISSION) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                HQVoiceMsgDownloadManager.getInstance().resumeDownloadService()
            } else {
                PermissionCheckUtil.showRequestPermissionFailedAlter(
                    this.context, permissions, grantResults
                )
            }
            return
        } else if (requestCode == PermissionCheckUtil.REQUEST_CODE_LOCATION_SHARE) {
            if (PermissionCheckUtil.checkPermissions(activity, permissions)) {
                var locationUiRender: LocationUiRender? = null
                for (processor in RongConfigCenter.conversationConfig().viewProcessors) {
                    if (processor is LocationUiRender) {
                        locationUiRender = processor
                        break
                    }
                }
                locationUiRender?.joinLocation()
            } else {
                if (activity != null) {
                    PermissionCheckUtil.showRequestPermissionFailedAlter(
                        activity, permissions, grantResults
                    )
                }
            }
        } else if (requestCode == MessageProviderPermissionHandler.REQUEST_CODE_ITEM_PROVIDER_PERMISSIONS) {
            MessageProviderPermissionHandler.getInstance()
                .onRequestPermissionsResult(activity, permissions, grantResults)
        }

        if (requestCode == PermissionCheckUtil.REQUEST_CODE_ASK_PERMISSIONS && grantResults.isNotEmpty() && grantResults[0] != PackageManager.PERMISSION_GRANTED) {
            PermissionCheckUtil.showRequestPermissionFailedAlter(
                this.context, permissions, grantResults
            )
        } else {
            mRongExtension!!.onRequestPermissionResult(requestCode, permissions, grantResults)
        }
    }

    private fun createLayoutManager(): RecyclerView.LayoutManager? {
        val linearLayoutManager: LinearLayoutManager = FixedLinearLayoutManager(
            context
        )
        linearLayoutManager.stackFromEnd = true
        return linearLayoutManager
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        if (activity == null || activity!!.intent == null) {
            RLog.e(
                TAG, "Must put targetId and conversation type to intent when start conversation."
            )
            return
        }
        if (!IMCenter.getInstance().isInitialized) {
            RLog.e(TAG, "Please init SDK first!")
            return
        }
        super.onViewCreated(view, savedInstanceState)

        mAdapter = onResolveAdapter()
        mNotificationContainer = rootView.findViewById(R.id.rc_notification_container)
        mNewMessageNum.setOnClickListener(this)
        mUnreadHistoryMessageNum.setOnClickListener(this)
        mUnreadMentionMessageNum.setOnClickListener(this)
        mLinearLayoutManager = createLayoutManager()
        if (mList != null) {
            mList!!.layoutManager = mLinearLayoutManager
        }
        mRefreshLayout.setOnTouchListener { v, event ->
            closeExpand()
            false
        }
        mAdapter?.setItemClickListener(object : BaseAdapter.OnItemClickListener {
            override fun onItemClick(view: View, holder: ViewHolder, position: Int) {
                closeExpand()
            }

            override fun onItemLongClick(
                view: View, holder: ViewHolder, position: Int
            ): Boolean {
                return false
            }
        })
        // 关闭动画
        mList?.adapter = mAdapter
        mList?.addOnScrollListener(mScrollListener)
        mList?.itemAnimator = null
        val gd = GestureDetector(context, object : SimpleOnGestureListener() {
            override fun onScroll(
                e1: MotionEvent, e2: MotionEvent, distanceX: Float, distanceY: Float
            ): Boolean {
                closeExpand()
                return super.onScroll(e1, e2, distanceX, distanceY)
            }
        })
        mList!!.addOnItemTouchListener(object : RecyclerView.OnItemTouchListener {
            override fun onInterceptTouchEvent(
                rv: RecyclerView, e: MotionEvent
            ): Boolean {
                return gd.onTouchEvent(e)
            }

            override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
                // Do nothing
            }

            override fun onRequestDisallowInterceptTouchEvent(
                disallowIntercept: Boolean
            ) {
                // Do nothing
            }
        })

        mRefreshLayout.isNestedScrollingEnabled = false
        mRefreshLayout.setRefreshHeader(IMRefreshHeader(requireContext()))
        //mRefreshLayout.setRefreshFooter(RongRefreshHeader(context))
        mRefreshLayout.setOnRefreshListener(this)
        mRefreshLayout.setOnLoadMoreListener(this)
        mRefreshLayout.setEnableRefresh(true)
        mRefreshLayout.setEnableLoadMore(false)

        initIntentExtra()
        if (Conversation.ConversationType.SYSTEM == conversationIdentifier!!.type) {
            mRongExtension!!.visibility = View.GONE
        } else {
            mRongExtension!!.visibility = View.VISIBLE
        }
        mMessageViewModel = ViewModelProvider(this).get(
            MessageViewModel::class.java
        )
        mRongExtensionViewModel = ViewModelProvider(this).get(
            RongExtensionViewModel::class.java
        )
        bindConversation(conversationIdentifier, mDisableSystemEmoji, mBundle)

        // NOTE: 2021/8/25  当初解决高清语音自动下载，现在高清语音下载不需要申请存储权限，删除此处.
        onViewCreated = true
    }

    private fun initIntentExtra() {
        val intent = activity!!.intent
        if (intent.hasExtra(RouteUtils.CONVERSATION_IDENTIFIER)) {
            val identifier =
                intent.getParcelableExtra<ConversationIdentifier>(RouteUtils.CONVERSATION_IDENTIFIER)
            if (identifier != null) {
                conversationIdentifier = identifier
            }
        }
        if (conversationIdentifier == null) {
            val typeValue = intent.getStringExtra(RouteUtils.CONVERSATION_TYPE)
            val type = Conversation.ConversationType.valueOf(
                typeValue!!.uppercase()
            )
            val targetId = intent.getStringExtra(RouteUtils.TARGET_ID)
            conversationIdentifier = ConversationIdentifier.obtain(type, targetId, "")
        }
        mDisableSystemEmoji = intent.getBooleanExtra(RouteUtils.DISABLE_SYSTEM_EMOJI, false)
        if (mBundle == null) {
            mBundle = intent.extras
        }
    }

    override fun onResume() {
        super.onResume()
        if (mMessageViewModel != null) mMessageViewModel!!.onResume()
        rootView.setOnKeyListener { v, keyCode, event ->
            if ((event.action == KeyEvent.ACTION_UP && keyCode == KeyEvent.KEYCODE_BACK)) {
                onBackPressed()
            } else false
        }
        mRongExtension!!.onResume()
    }

    override fun onPause() {
        super.onPause()
        if (mMessageViewModel != null) mMessageViewModel!!.onPause()
        if (mRongExtension != null) mRongExtension!!.onPause()
    }

    override fun onStart() {
        super.onStart()
        // 保存 activity 的原 softInputMode
        val activity = activity
        if (activity != null) {
            activitySoftInputMode = activity.window.attributes.softInputMode
            if (mRongExtension != null && mRongExtension!!.useKeyboardHeightProvider()) {
                resetSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
            } else {
                resetSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
            }
        }
    }

    override fun onStop() {
        super.onStop()
        if (mMessageViewModel != null) mMessageViewModel!!.onStop()
        resetSoftInputMode(activitySoftInputMode)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        AppModule.selectEmojiId = ""
        for (processor in RongConfigCenter.conversationConfig().viewProcessors) {
            processor.onDestroy()
        }
        mList!!.removeOnScrollListener(mScrollListener)
        if (mMessageViewModel != null) {
            mMessageViewModel!!.pageEventLiveData.removeObserver(mPageObserver)
            mMessageViewModel!!.uiMessageLiveData.removeObserver(mListObserver)
            mMessageViewModel!!.newMentionMessageUnreadLiveData.removeObserver(
                mNewMentionMessageUnreadObserver
            )
            mMessageViewModel!!.onDestroy()
        }
        mRongExtension?.onDestroy()
        bindToConversation = false
        mAdapter?.onViewDestroy()
        closeCountDownTimer()
    }

    /**
     * 关闭所有倒计时
     */
    private fun closeCountDownTimer() {
        countDownMap.forEach { _, value ->
            value?.cancelCountDownAndRemoveListener()
        }
    }

    private fun resetSoftInputMode(mode: Int) {
        val activity = activity
        activity?.window?.setSoftInputMode(mode)
    }

    override fun onClick(v: View) {
        val id = v.id
        if (id == R.id.mNewMessageNum) {
            if (mMessageViewModel != null) mMessageViewModel!!.newMessageBarClick()
        } else if (id == R.id.mUnreadHistoryMessageNum) {
            if (mMessageViewModel != null) mMessageViewModel!!.unreadBarClick()
        } else if (id == io.rong.imkit.R.id.rc_mention_message_count) {
            if (mMessageViewModel != null) mMessageViewModel!!.newMentionMessageBarClick()
        }
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        if (mMessageViewModel != null && bindToConversation) {
            mMessageViewModel!!.onLoadMore()
        }
    }

    /**
     * 调用了tool/getusersinfo之后的回调
     */
    fun onUserRefresh() {
        if (mRongExtension != null) {
            mRongExtension.refreshGreetBtnStatus()
        }
    }

    /**
     * 提示dialog. 例如"加入聊天室失败"的dialog 用户自定义此dialog的步骤: 1.定义一个类继承自 ConversationFragment 2.重写
     * onWarningDialog
     *
     * @param msg dialog 提示
     */
    fun onWarningDialog(msg: String?) {
        val builder = AlertDialog.Builder(activity)
        builder.setCancelable(false)
        val alertDialog = builder.create()
        alertDialog.show()
        val window = alertDialog.window ?: return
        window.setContentView(io.rong.imkit.R.layout.rc_cs_alert_warning)
        val tv = window.findViewById<TextView>(io.rong.imkit.R.id.rc_cs_msg)
        tv.text = msg
        window.findViewById<View>(io.rong.imkit.R.id.rc_btn_ok)
            .setOnClickListener(View.OnClickListener {
                alertDialog.dismiss()
                if (!isAdded) {
                    return@OnClickListener
                }
                val fm = childFragmentManager
                if (fm.backStackEntryCount > 0) {
                    fm.popBackStack()
                } else {
                    if (activity != null) {
                        activity!!.finish()
                    }
                }
            })
    }

    private fun showLoadMessageDialog(
        callback: GetMessageCallback?, list: List<Message>
    ) {
        AlertDialog.Builder(activity, AlertDialog.THEME_DEVICE_DEFAULT_LIGHT)
            .setMessage(getString(io.rong.imkit.R.string.rc_load_local_message)).setPositiveButton(
                getString(io.rong.imkit.R.string.rc_dialog_ok)
            ) { dialog, which -> callback?.onSuccess(list, true) }.setNegativeButton(
                getString(io.rong.imkit.R.string.rc_cancel)
            ) { dialog, which -> callback?.onErrorAsk(list) }.show()
    }

    private fun showCurEmojiDialog() {
        context.routeAction(ActionType.actionMsgEmojiDialog)
    }

    private fun showGreetDialog() {
        context.routeAction(ActionEntity().apply {
            id = ActionType.actionMsgGreetDialog
            param = IMChatIntent().apply {
                user_id = conversationIdentifier?.targetId!!
            }
        })
    }

    private fun showGiftDialog() {
        context.routeAction(ActionEntity().apply {
            id = ActionType.actionMsgGiftDialog
            param = IMChatIntent().apply {
                user_id = conversationIdentifier?.targetId.orEmpty()
                channelId = conversationIdentifier?.channelId.orEmpty()
                type = conversationIdentifier?.type ?: Conversation.ConversationType.PRIVATE
            }
        })
    }

    /**
     * 是否对用户隐身
     */
    private fun userIsInVisible() {
        var userTimeZoneId = getString(R.string.conversation_timezone)
        val userInfo =
            RongUserInfoManager.getInstance().getUserInfo(conversationIdentifier?.targetId)
        if (userInfo == null || userInfo.extra.isNullOrEmpty()) openInVisible(false)
        userInfo?.extra?.jsonToObj<UserImExtraEntity>()?.let {
            openInVisible(
                isFlagEnabled(it.chatFlag, ConnectFlagInvisible)
                        || isFlagEnabled(it.chatFlag, ConnectFlagProhibition)
                        || isBlocked()
            )
            userTimeZoneId = it.timeZoneID.ifEmpty { getString(R.string.conversation_timezone) }
        }
        setUserTime(userTimeZoneId)
    }

    /**
     * 设置对用户隐身是调用
     * @param open true: 隐身 false: 不隐身
     */
    fun openInVisible(open: Boolean) {
        mRongExtension?.setIsInVisible(open)
        mRongExtension?.showInputView(!open)
    }

    private fun setUserTime(timeZoneID: String) {
        mRongExtension?.inputPanel?.mUserTime?.also {
            it.timeZone = timeZoneID
            // TextClock强制按某种格式显示时，需将其他模式的格式设置为null
            it.format12Hour = null
            it.format24Hour = "HH:mm"
        }
        mRongExtension?.inputPanel?.mllUserTime?.show(timeZoneID.isNotEmpty())
    }

    private fun isBlocked(): Boolean {
        return (activity as ChatActivity).isBlocked
    }

    private fun closeExpand() {
        if (mRongExtensionViewModel != null) {
            mRongExtensionViewModel!!.collapseExtensionBoard()
        }
    }

    /**
     * 获取 adapter. 可复写此方法实现自定义 adapter.
     *
     * @return 会话列表 adapter
     */
    protected fun onResolveAdapter(): MessageListAdapter? {
        return MessageListAdapter(this, this)
    }

    fun openAlbumSelector(requestCode: Int, type: Int) {
        val context = AppContext.getTopActivity()
        context?.let {
            try {
                val intent = Intent(it, PrivacyListActivity::class.java)
                intent.putExtra(
                    Values.intentJsonParam, PrivacyListIntent().also { intent ->
                        intent.request_code = requestCode
                        intent.type = type
                        intent.imId = conversationIdentifier?.targetId.orEmpty()
                    }.toJsonString()
                )
                this.startActivityForResult(intent, requestCode)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /** @param view 自定义列表 header view
     */
    fun addHeaderView(view: View?) {
        mAdapter!!.addHeaderView(view)
    }

    /** @param view 自定义列表 footer view
     */
    fun addFooterView(view: View?) {
        mAdapter!!.addFootView(view)
    }

    /** @param view 自定义列表 空数据 view
     */
    fun setEmptyView(view: View?) {
        mAdapter!!.setEmptyView(view)
    }

    /** @param emptyId 自定义列表 空数据的 LayoutId
     */
    fun setEmptyView(@LayoutRes emptyId: Int) {
        mAdapter!!.setEmptyView(emptyId)
    }
}
