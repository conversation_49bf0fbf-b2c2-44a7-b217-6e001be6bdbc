package com.fascin.chatter.im.view

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.net.Uri
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.fascin.chatter.R
import com.fascin.chatter.bean.IMAlbumMsgData
import com.fascin.chatter.bean.event.PrivacyCountDownEvent
import com.fascin.chatter.bean.event.PrivacyLookEvent
import com.fascin.chatter.config.Config
import com.fascin.chatter.im.ImageActivity
import com.fascin.chatter.im.msg.FasPrivacyAlbumMsg
import com.iandroid.allclass.lib_common.GlideLoader
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toLongEx
import com.iandroid.allclass.lib_common.utils.timer.IActionListener
import com.iandroid.allclass.lib_common.utils.timer.ICountDownListener
import com.iandroid.allclass.lib_common.utils.timer.TAG_RV_ITEM
import io.rong.common.RLog
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import jp.wasabeef.glide.transformations.BlurTransformation
import kotlinx.android.synthetic.main.item_chat_privacy_album.view.flag_video
import kotlinx.android.synthetic.main.item_chat_privacy_album.view.ll_rc_privacy
import kotlinx.android.synthetic.main.item_chat_privacy_album.view.rc_image
import kotlinx.android.synthetic.main.item_chat_privacy_album.view.rc_privacy_text
import kotlinx.android.synthetic.main.item_chat_privacy_album.view.rc_text
import kotlinx.android.synthetic.main.item_chat_privacy_album.view.rc_timer
import kotlinx.android.synthetic.main.item_chat_privacy_album.view.rl_content

class FasPirvacyAlbumProvider : BaseMessageItemProvider<FasPrivacyAlbumMsg>() {
    val TAG = "ImageMessageItemProvide"

    init {
        mConfig.showContentBubble = false
        mConfig.showProgress = false
        mConfig.showReadState = false
    }

    private var actionListener: IActionListener? = null
    private var countDownCallbackMap: HashMap<Int, ICountDownListener>? = null

    override fun initUserInfo(
        holder: ViewHolder?,
        uiMessage: UiMessage?,
        position: Int,
        listener: IViewProviderListener<UiMessage>?,
        isSender: Boolean
    ) {
        mConfig.showPortrait = !privacyIsDestroyed(getPrivacyMsgStatus(uiMessage))
        super.initUserInfo(holder, uiMessage, position, listener, isSender)
    }

    override fun initContent(
        holder: ViewHolder?,
        isSender: Boolean,
        uiMessage: UiMessage?,
        position: Int,
        listener: IViewProviderListener<UiMessage>?,
        list: MutableList<UiMessage>?
    ) {
        mConfig.centerInHorizontal = privacyIsDestroyed(getPrivacyMsgStatus(uiMessage))
        super.initContent(holder, isSender, uiMessage, position, listener, list)
    }

    override fun onCreateMessageContentViewHolder(parent: ViewGroup, viewType: Int): ViewHolder? {
        val view: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.item_chat_privacy_album, parent, false)
        return ViewHolder(view.context, view)
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder?,
        message: FasPrivacyAlbumMsg,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage?>?,
        listener: IViewProviderListener<UiMessage?>?
    ) {
        holder?.convertView?.run {
            if (!checkViewsValid(rc_image)) {
                RLog.e(TAG, "checkViewsValid error," + uiMessage.objectName)
                return
            }
            var msgData = message?.content?.jsonToObj<IMAlbumMsgData>()
            msgData?.also {
                val status = getPrivacyMsgStatus(uiMessage)
                if (privacyIsCanLook(status)) {
                    // 更正 消息在open状态下断网后，本地消息无法更新时的UI设置
                    if (getPrivacyDeadTime(uiMessage) <= 0) {
                        setDestroyedViewUI(holder, uiMessage)
                        return
                    }
                    rc_timer.show(true)
                    ll_rc_privacy.show(false)
                    rl_content.show(true)
                    rc_text.show(false)
                    loadImage(rl_content, rc_image, it.getImageUrl(), 0)
                    flag_video.show(it.type == Config.MediaVideo)
                    // 配置倒计时监听
                    val countDownCallBack = getCountDownCallBack(holder, uiMessage)
                    actionListener?.getCountDownTimer(TAG_RV_ITEM)?.showCountDownTimer(
                        getPrivacyDeadTime(uiMessage), countDownCallBack
                    )
                } else if (privacyIsDestroyed(status)) {
                    setDestroyedViewUI(holder, uiMessage)
                } else {
                    rc_timer.show(false)
                    ll_rc_privacy.show(true)
                    rl_content.show(true)
                    rc_text.show(false)
                    // 视频模糊度比图片低
                    loadImage(
                        rl_content,
                        rc_image,
                        it.getImageUrl(),
                        if (it.type == Config.MediaVideo) GlideLoader.videoBlurRadius else GlideLoader.imgBlurRadius
                    )
                    flag_video.show(it.type == Config.MediaVideo)
                    rc_privacy_text.setText(
                        if (it.type == Config.MediaVideo) R.string.privacy_tag_video
                        else R.string.privacy_tag_photo
                    )
                }
            }
        }
    }

    private fun loadImage(layoutRootView: View, imageView: ImageView, url: String, blur: Int = 0) {
        if (blur > 0) {
            Glide.with(imageView)
                .load(Uri.parse(url))
                .placeholder(com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_r)
                .error(com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_r)
                .transform(BlurTransformation(blur))
                .listener(
                    object : RequestListener<Drawable?> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any,
                            target: Target<Drawable?>,
                            isFirstResource: Boolean
                        ): Boolean {
                            layoutRootView.iMImageViewMeasureLayoutParams(null)
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable?,
                            model: Any,
                            target: Target<Drawable?>,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            layoutRootView.iMImageViewMeasureLayoutParams(resource)
                            return false
                        }
                    })
                .into(imageView)
        } else {
            Glide.with(imageView)
                .load(Uri.parse(url))
                .placeholder(com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_r)
                .error(com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_r)
                .listener(
                    object : RequestListener<Drawable?> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any,
                            target: Target<Drawable?>,
                            isFirstResource: Boolean
                        ): Boolean {
                            layoutRootView.iMImageViewMeasureLayoutParams(null)
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable?,
                            model: Any,
                            target: Target<Drawable?>,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            layoutRootView.iMImageViewMeasureLayoutParams(resource)
                            return false
                        }
                    })
                .into(imageView)
        }
    }


    override fun onItemClick(
        holder: ViewHolder,
        imageMessage: FasPrivacyAlbumMsg?,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage?>?,
        listener: IViewProviderListener<UiMessage?>?
    ): Boolean {
        imageMessage?.content?.jsonToObj<IMAlbumMsgData>()?.also { msgData ->
            var status = getPrivacyMsgStatus(uiMessage)
            if (privacyIsDestroyed(status)) {
                return true
            }

            if (!privacyIsCanLook(status)
                && !UserController.isMe(uiMessage?.message?.senderUserId.orEmpty())
            ) {
                SimpleRxBus.post(
                    PrivacyLookEvent(
                        uiMessage.message.uId, msgData.original_msgid,
                        msgData.id.toInt()
                    )
                )
            } else {
                val intent = Intent(holder.context, ImageActivity::class.java)
                intent.putExtra("message", uiMessage.message)
                holder.context.startActivity(intent)
            }
        }
        return true
    }

    override fun isMessageViewType(messageContent: MessageContent): Boolean {
        return messageContent is FasPrivacyAlbumMsg && !messageContent.isDestruct()
    }

    override fun getSummarySpannable(
        context: Context,
        imageMessage: FasPrivacyAlbumMsg?
    ): Spannable? {
        imageMessage?.content?.jsonToObj<IMAlbumMsgData>()?.also {
            if (it.type == Config.MediaImage) {//私密图片
                return SpannableString(
                    context.getString(R.string.privacy_image)
                )
            } else if (it.type == Config.MediaVideo) {//私密视频
                return SpannableString(
                    context.getString(R.string.privacy_video)
                )
            }
        }
        return SpannableString(
            context.getString(R.string.privacy_file)
        )
    }

    override fun setActionListener(actionListener: IActionListener?) {
        this.actionListener = actionListener
    }

    /**
     * 给对应holder创建回调
     */
    private fun getCountDownCallBack(
        holder: ViewHolder?,
        uiMessage: UiMessage
    ): ICountDownListener {
        if (countDownCallbackMap == null)
            countDownCallbackMap = HashMap()

        val call = object : ICountDownListener {
            override fun onCountDownTick(
                day: Long,
                hour: Long,
                minute: Long,
                second: Long
            ) {
                val timeStr = "${getDouble(hour)}:${getDouble(minute)}:${getDouble(second)}"
                // 通知播放页更新
                SimpleRxBus.post(PrivacyCountDownEvent(uiMessage.messageId, timeStr, false))
                // 每秒更新一次
                holder?.convertView?.rc_timer?.text = timeStr
            }

            override fun onCountDownFinish() {
                // 倒计时结束
                setDestroyedViewUI(holder, uiMessage)
                //清除对应监听
                clearCallByMessageId(uiMessage.messageId)
                // 通知播放页停止展示
                SimpleRxBus.post(PrivacyCountDownEvent(uiMessage.messageId, "", true))
            }
        }

        clearCallByMessageId(uiMessage.messageId)
        countDownCallbackMap?.put(uiMessage.messageId, call)
        // 注册回调
        actionListener?.getCountDownTimer(TAG_RV_ITEM)?.register(
            call,
            getPrivacyDeadTime(uiMessage)
        )
        return call
    }

    /**
     * 设置私密消息已销毁时的UI
     */
    private fun setDestroyedViewUI(holder: ViewHolder?, uiMessage: UiMessage) {
        holder?.convertView?.run {
            rc_timer.show(false)
            rl_content.show(false)
            rc_text.show(true)
            flag_video.show(false)

            uiMessage.message?.let {
                (it.content as FasPrivacyAlbumMsg).content.orEmpty().jsonToObj<IMAlbumMsgData>()
                    ?.also { messageData ->
                        if (it.messageDirection == Message.MessageDirection.SEND) {
                            rc_text.text =
                                context.getString(
                                    if (messageData.type == Config.MediaImage) R.string.privacy_photo_destroyed
                                    else R.string.privacy_video_destroyed
                                )
                        } else {
                            rc_text.text = String.format(
                                context.getString(
                                    if (messageData.type == Config.MediaImage) R.string.privacy_photo_destroyed_re
                                    else R.string.privacy_video_destroyed_re
                                ),
                                uiMessage.userInfo.name
                            )
                        }
                    }
            }
        }
    }

    /**
     * 清楚指定message对应的回调
     */
    private fun clearCallByMessageId(messageId: Int) {
        if (countDownCallbackMap?.get(messageId) != null) {
            actionListener?.getCountDownTimer(TAG_RV_ITEM)
                ?.unregister(countDownCallbackMap?.get(messageId)!!)
            countDownCallbackMap?.remove(messageId)
        }
    }

    /**
     * 页面销毁，置空变量
     */
    override fun onViewDestroy() {
        actionListener = null
        countDownCallbackMap?.clear()
    }

    /**
     * 获取消息状态 0 未打开  1已打开  2 已销毁
     */
    private fun getPrivacyMsgStatus(uiMessage: UiMessage?): Long {
        if (uiMessage?.expansion != null) {
            for ((k, v) in uiMessage.expansion) {
                if (k == "status") {
                    return v.toLongEx(0)
                }
            }
        }

        return 0
    }

    /**
     * 计算剩余倒计时
     */
    private fun getPrivacyDeadTime(uiMessage: UiMessage): Long {
        if (uiMessage.expansion != null) {
            for ((k, v) in uiMessage.expansion) {
                if (k == "del_time") {
                    val countdown = v.toLongEx(0) * 1000 - System.currentTimeMillis()
                    return if (countdown > 0) countdown else 0
                }
            }
        }
        return 0
    }

    private fun privacyIsDestroyed(status: Long): Boolean {
        return status == 2L
    }

    private fun privacyIsCanLook(status: Long): Boolean {
        return status == 1L
    }

    private fun senderIsMe(uiMessage: UiMessage?): Boolean {
        return uiMessage?.run {
            UserController.isMe(message?.senderUserId.orEmpty())
        } ?: false
    }

    private fun getDouble(value: Long): String {
        return if (value < 10) "0$value" else value.toString()
    }
}
