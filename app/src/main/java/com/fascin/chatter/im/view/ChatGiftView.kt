package com.fascin.chatter.im.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.motion.widget.MotionLayout
import com.bumptech.glide.Glide
import com.fascin.chatter.bean.chat.GiftEntity
import com.fascin.chatter.databinding.ViewChatGiftBinding
import com.fascin.chatter.im.msg.GiftMessage
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import io.rong.imkit.model.UiMessage

class ChatGiftView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : MotionLayout(context, attrs) {

    private val binding: ViewChatGiftBinding = ViewChatGiftBinding.inflate(LayoutInflater.from(context), this, true)


    fun bindData(uiMessage: UiMessage?) {
        if (uiMessage == null) return

        val isOpened = isGiftOpened(uiMessage)

        bindData2View(uiMessage)
        binding.root.setTransition(if (isOpened) 1 else 0)
    }

    private fun bindData2View(uiMessage: UiMessage) {
        val messageContent: GiftMessage = uiMessage.message.content as? GiftMessage ?: return
        val data = messageContent.content.jsonToObj<GiftEntity>() ?: return

        binding.tvGiftName.text = data.name
        Glide.with(context).asGif().load(data.gif).into(binding.ivGiftOpened)
    }

    /**
     * 获取消息状态 0 未打开  1已打开
     */
    private fun isGiftOpened(uiMessage: UiMessage?): Boolean {
        var status = 0

        if (uiMessage?.expansion != null) {
            for ((k, v) in uiMessage.expansion) {
                if (k == "status") {
                    status = v.toIntOrNull() ?: 0
                }
            }
        }
        return status == 1
    }
}