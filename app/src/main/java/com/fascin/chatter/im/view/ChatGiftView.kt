package com.fascin.chatter.im.view

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.motion.widget.MotionLayout
import com.bumptech.glide.Glide
import com.fascin.chatter.R
import com.fascin.chatter.bean.chat.GiftEntity
import com.fascin.chatter.databinding.ViewChatGiftBinding
import com.fascin.chatter.im.msg.GiftMessage
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import io.rong.imkit.model.UiMessage

class ChatGiftView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : MotionLayout(context, attrs) {

    private val binding: ViewChatGiftBinding = ViewChatGiftBinding.inflate(LayoutInflater.from(context), this, true)
    private val handler = Handler(Looper.getMainLooper())
    private var animationCallback: (() -> Unit)? = null


    fun bindData(uiMessage: UiMessage?) {
        if (uiMessage == null) return

        val isOpened = isGiftOpened(uiMessage)

        bindData2View(uiMessage)

        // 根据礼物状态设置初始状态
        if (isOpened) {
            // 已拆封，直接显示完成状态
            setTransition(R.id.transition_opened_to_finished)
            progress = 1.0f
        } else {
            // 未拆封，设置为初始状态
            setTransition(R.id.transition_unopened_to_opening)
            progress = 0.0f
        }
    }

    /**
     * 开始拆封动画
     * @param callback 动画完成后的回调
     */
    fun play(callback: (() -> Unit)? = null) {
        animationCallback = callback
        startUnwrapAnimation()
    }

    private fun bindData2View(uiMessage: UiMessage) {
        val messageContent: GiftMessage = uiMessage.message.content as? GiftMessage ?: return
        val data = messageContent.content.jsonToObj<GiftEntity>() ?: return

        binding.tvGiftName.text = data.name
        Glide.with(context).asGif().load(data.gif).into(binding.ivGift)
    }

    /**
     * 开始拆封动画序列
     */
    private fun startUnwrapAnimation() {
        // 设置动画监听器
        setTransitionListener(object : TransitionListener {
            override fun onTransitionStarted(motionLayout: MotionLayout?, startId: Int, endId: Int) {}

            override fun onTransitionChange(motionLayout: MotionLayout?, startId: Int, endId: Int, progress: Float) {}

            override fun onTransitionCompleted(motionLayout: MotionLayout?, currentId: Int) {
                when (currentId) {
                    R.id.opening -> {
                        // 拆封中动画完成，立即切换到拆封后状态
                        setTransition(R.id.transition_opening_to_opened)
                        transitionToEnd()
                    }
                    R.id.opened -> {
                        // 拆封后状态，延迟1秒后开始最终动画
                        handler.postDelayed({
                            setTransition(R.id.transition_opened_to_finished)
                            transitionToEnd()
                        }, 1000)
                    }
                    R.id.finished -> {
                        // 所有动画完成
                        animationCallback?.invoke()
                        setTransitionListener(null) // 清除监听器
                    }
                }
            }

            override fun onTransitionTrigger(motionLayout: MotionLayout?, triggerId: Int, positive: Boolean, progress: Float) {}
        })

        // 开始第一个动画：未拆封 -> 拆封中 (2秒)
        setTransition(R.id.transition_unopened_to_opening)
        transitionToEnd()
    }

    /**
     * 获取消息状态 0 未打开  1已打开
     */
    private fun isGiftOpened(uiMessage: UiMessage?): Boolean {
        var status = 0

        if (uiMessage?.expansion != null) {
            for ((k, v) in uiMessage.expansion) {
                if (k == "status") {
                    status = v.toIntOrNull() ?: 0
                }
            }
        }
        return status == 1
    }
}