package com.fascin.chatter.im.view

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.net.Uri
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.fascin.chatter.R
import com.fascin.chatter.bean.IMAlbumMsgData
import com.fascin.chatter.config.Config
import com.fascin.chatter.im.ImageActivity
import com.fascin.chatter.im.msg.FasPublicAlbumMsg
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.show
import io.rong.common.RLog
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.MessageContent
import kotlinx.android.synthetic.main.item_chat_public_album.view.flag_video
import kotlinx.android.synthetic.main.item_chat_public_album.view.rc_image

class FasPublicAlbumProvider : BaseMessageItemProvider<FasPublicAlbumMsg>() {
    init {
        mConfig.showContentBubble = false
        mConfig.showProgress = false
        mConfig.showReadState = false
    }

    override fun onCreateMessageContentViewHolder(parent: ViewGroup, viewType: Int): ViewHolder? {
        val view: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.item_chat_public_album, parent, false)
        return ViewHolder(view.context, view)
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder?,
        message: FasPublicAlbumMsg,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage?>?,
        listener: IViewProviderListener<UiMessage?>?
    ) {
        holder?.convertView?.run {
            if (!checkViewsValid(rc_image)) {
                RLog.e(TAG, "checkViewsValid error," + uiMessage.objectName)
                return
            }
            var msgData = message?.content?.jsonToObj<IMAlbumMsgData>()
            msgData?.also {
                Glide.with(rc_image)
                    .load(Uri.parse(it.getImageUrl()))
                    .placeholder(com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_r)
                    .error(com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_r)
                    .override(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL)
                    .listener(
                        object : RequestListener<Drawable?> {
                            override fun onLoadFailed(
                                e: GlideException?,
                                model: Any,
                                target: Target<Drawable?>,
                                isFirstResource: Boolean
                            ): Boolean {
                                rc_image.iMImageViewMeasureLayoutParams(null)
                                return false
                            }

                            override fun onResourceReady(
                                resource: Drawable?,
                                model: Any,
                                target: Target<Drawable?>,
                                dataSource: DataSource,
                                isFirstResource: Boolean
                            ): Boolean {
                                rc_image.iMImageViewMeasureLayoutParams(resource)
                                return false
                            }
                        })
                    .into(rc_image)
                flag_video.show(it.type == Config.MediaVideo)
            }
        }
    }

    override fun onItemClick(
        holder: ViewHolder,
        imageMessage: FasPublicAlbumMsg?,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage?>?,
        listener: IViewProviderListener<UiMessage?>?
    ): Boolean {
        val intent = Intent(holder.context, ImageActivity::class.java)
        intent.putExtra("message", uiMessage.message)
        holder.context.startActivity(intent)
        return true
    }

    override fun onItemLongClick(
        holder: ViewHolder?,
        t: FasPublicAlbumMsg?,
        uiMessage: UiMessage?,
        position: Int,
        list: MutableList<UiMessage>?,
        listener: IViewProviderListener<UiMessage>?
    ): Boolean {
        return false
    }

    override fun isMessageViewType(messageContent: MessageContent): Boolean {
        return messageContent is FasPublicAlbumMsg && !messageContent.isDestruct()
    }

    override fun getSummarySpannable(
        context: Context,
        imageMessage: FasPublicAlbumMsg?
    ): Spannable? {
        return imageMessage?.content?.jsonToObj<IMAlbumMsgData>()?.takeIf { it.type == Config.MediaVideo }?.run {
            SpannableString(context.getString(R.string.im_conversation_summary_content_video))
        } ?: SpannableString(
            context.getString(R.string.im_conversation_summary_content_image)
        )
    }
}
