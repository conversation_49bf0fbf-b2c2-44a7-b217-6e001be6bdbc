package com.fascin.chatter.bean.event

import com.fascin.chatter.bean.CourseEntity

data class UIEventIMFlashChatSuccess(var userId: String)

data class UIEventUserBlock(var userId: String)

data class UIEventHomeLikesTab(var tabIndex: Int)

data class LoginFinishEvent(val finish: Boolean = false)

data class MessageSettingChangeEvent(var type: Int)

data class PrivacyLookEvent(var to_msgid: String, var from_msgid: String, var media_id: Int)

/**
 * 礼物开启事件
 */
data class GiftOpenEvent(val toMsgId: String, val fromMsgId: String)

/**
 * 私密消息倒计时
 * @param msgId 哪条消息对应倒计时
 * @param timeTask 倒计时时间str
 * @param isFinish true:正在倒计时
 */
data class PrivacyCountDownEvent(var msgId: Int, var timeTask: String, var isFinish: Boolean)

/**
 * 聊天页面招呼语选择
 * @param greetId 选择的招呼语id
 * @param content 招呼语内容
 */
data class GreetSelectedEvent(var greetId: Int, var content: String)

/**
 * 聊天页面emoji组合选择
 * @param emojiId 选择的emoji id
 * @param content emoji组合内容
 */
data class EmojiSelectedEvent(var emojiId: Int, var content: String)