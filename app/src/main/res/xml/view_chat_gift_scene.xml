<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- 状态1: 未拆封状态 -->
    <ConstraintSet android:id="@+id/unopened">
        <Constraint android:id="@+id/bg">
            <PropertySet android:alpha="1.0" />
        </Constraint>
        <Constraint android:id="@+id/bgCover">
            <PropertySet android:alpha="0.0" />
        </Constraint>
        <Constraint android:id="@+id/ivGift">
            <PropertySet android:alpha="0.0" />
        </Constraint>
        <Constraint android:id="@+id/bgGiftBox">
            <PropertySet android:alpha="1.0" />
            <Transform android:rotation="0" />
        </Constraint>
        <Constraint android:id="@+id/tvGiftName">
            <PropertySet android:alpha="1.0" />
            <CustomAttribute
                app:attributeName="textSize"
                app:customDimension="14sp" />
        </Constraint>
    </ConstraintSet>

    <!-- 状态2: 拆封中状态 -->
    <ConstraintSet android:id="@+id/opening">
        <Constraint android:id="@+id/bg">
            <PropertySet android:alpha="1.0" />
        </Constraint>
        <Constraint android:id="@+id/bgCover">
            <PropertySet android:alpha="1.0" />
        </Constraint>
        <Constraint android:id="@+id/ivGift">
            <PropertySet android:alpha="1.0" />
        </Constraint>
        <Constraint android:id="@+id/bgGiftBox">
            <PropertySet android:alpha="1.0" />
            <Transform android:rotation="180" />
        </Constraint>
        <Constraint android:id="@+id/tvGiftName">
            <PropertySet android:alpha="0.0" />
            <CustomAttribute
                app:attributeName="textSize"
                app:customDimension="14sp" />
        </Constraint>
    </ConstraintSet>

    <!-- 状态3: 拆封后状态 -->
    <ConstraintSet android:id="@+id/opened">
        <Constraint android:id="@+id/bg">
            <PropertySet android:alpha="1.0" />
        </Constraint>
        <Constraint android:id="@+id/bgCover">
            <PropertySet android:alpha="1.0" />
        </Constraint>
        <Constraint android:id="@+id/ivGift">
            <PropertySet android:alpha="1.0" />
            <Layout
                android:layout_width="74dp"
                android:layout_height="74dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />
        </Constraint>
        <Constraint android:id="@+id/bgGiftBox">
            <PropertySet android:alpha="0.0" />
        </Constraint>
        <Constraint android:id="@+id/tvGiftName">
            <PropertySet android:alpha="1.0" />
            <Layout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ivGift" />
            <CustomAttribute
                app:attributeName="textSize"
                app:customDimension="16sp" />
        </Constraint>
    </ConstraintSet>

    <!-- 状态4: 完成状态 -->
    <ConstraintSet android:id="@+id/finished">
        <Constraint android:id="@+id/bg">
            <PropertySet android:alpha="0.0" />
        </Constraint>
        <Constraint android:id="@+id/bgCover">
            <PropertySet android:alpha="0.0" />
        </Constraint>
        <Constraint android:id="@+id/ivGift">
            <PropertySet android:alpha="1.0" />
            <Layout
                android:layout_width="74dp"
                android:layout_height="74dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />
        </Constraint>
        <Constraint android:id="@+id/bgGiftBox">
            <PropertySet android:alpha="0.0" />
        </Constraint>
        <Constraint android:id="@+id/tvGiftName">
            <PropertySet android:alpha="0.0" />
            <CustomAttribute
                app:attributeName="textSize"
                app:customDimension="16sp" />
        </Constraint>
    </ConstraintSet>

    <!-- 过渡1: 未拆封 -> 拆封中 (2秒) -->
    <Transition
        android:id="@+id/transition_unopened_to_opening"
        app:constraintSetStart="@id/unopened"
        app:constraintSetEnd="@id/opening"
        app:duration="2000">
        <KeyFrameSet>
            <KeyAttribute
                app:motionTarget="@id/bgGiftBox"
                app:framePosition="0"
                android:rotation="0" />
            <KeyAttribute
                app:motionTarget="@id/bgGiftBox"
                app:framePosition="100"
                android:rotation="180" />
        </KeyFrameSet>
    </Transition>

    <!-- 过渡2: 拆封中 -> 拆封后 (瞬间) -->
    <Transition
        android:id="@+id/transition_opening_to_opened"
        app:constraintSetStart="@id/opening"
        app:constraintSetEnd="@id/opened"
        app:duration="100" />

    <!-- 过渡3: 拆封后 -> 完成 (1秒渐隐) -->
    <Transition
        android:id="@+id/transition_opened_to_finished"
        app:constraintSetStart="@id/opened"
        app:constraintSetEnd="@id/finished"
        app:duration="1000" />

</MotionScene>
