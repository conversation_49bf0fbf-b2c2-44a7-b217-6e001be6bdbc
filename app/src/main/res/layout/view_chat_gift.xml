<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:layoutDescription="@xml/view_chat_gift_scene">

    <ImageView
        android:id="@+id/bg"
        android:src="@mipmap/bg_gift_unwrap"
        android:layout_width="160dp"
        android:layout_height="150dp"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/bgCover"
        android:background="@drawable/bg_gift_opening"
        android:layout_width="148dp"
        android:layout_height="105dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ImageView
        android:id="@+id/ivGift"
        android:layout_width="61dp"
        android:layout_height="61dp"
        android:layout_marginBottom="10dp"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView
        android:id="@+id/bgGiftBox"
        android:src="@mipmap/ic_gift_unwrap_normal"
        android:layout_width="90.75dp"
        android:layout_height="75dp"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/tvGiftName"
        android:textSize="14sp"
        android:textColor="#8F5A0A"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="Gift Name"
        android:layout_marginBottom="10dp"
        app:layout_constraintStart_toStartOf="@id/bgGiftBox"
        app:layout_constraintTop_toTopOf="@id/bgGiftBox"
        app:layout_constraintEnd_toEndOf="@id/bgGiftBox"
        app:layout_constraintBottom_toBottomOf="@id/bgGiftBox"/>

</androidx.constraintlayout.motion.widget.MotionLayout>